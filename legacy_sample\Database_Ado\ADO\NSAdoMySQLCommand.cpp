#include "stdafx.h"
#include "NSAdoMySQLCommand.h"

#include "NSAdoRecordset.h"
#include "QueryData/NSPQueryData.h"

NSAdoMySQLCommand::NSAdoMySQLCommand()
{
}

NSAdoMySQLCommand::~NSAdoMySQLCommand()
{
}

bool NSAdoMySQLCommand::Execute(NSPQueryData* pcQueryData)
{
	if (pcQueryData == nullptr)
		return false;
	if (false == pcQueryData->SetAdoCommand(this))
		return false;
	NSAdoRecordset* pcAdoRecordSet = pcQueryData->GetAdoRecordSet(pcQueryData->GetQuerySetCount() - 1);
	if (pcAdoRecordSet == nullptr)
		return false;
	pcAdoRecordSet->SetCommandName(GetCommandName());
	m_iReturnValue = -100;
	try
	{
		m_pCommand->ActiveConnection = m_pConnection;
		if (m_pConnection->GetState() != adStateOpen)
			return false;
		_RecordsetPtr pRs = m_pCommand->Execute(nullptr, nullptr, adCmdStoredProc);
		if (pRs->GetState() == adStateOpen)
		{
			pRs->PutRefActiveConnection(nullptr);
			pcAdoRecordSet->SetRecordset(pRs);
			pRs->Close();
		}
		GetItem("ReturnCode", m_iReturnValue);
		pRs.Release();
		pRs = nullptr;
		//PrintQuery(m_iReturnValue, 0);
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "EXECUTE ERROR");
		PrintQuery(m_iReturnValue, e.Error());
		if ((e.Error() == 0x80004005 || e.Error() == 0x8007000e || e.Error() == 0x800A0E7D) && m_pConnection->GetState() == adStateOpen)
		{
			m_pConnection->Close();
		}
		return false;
	}
	catch (std::exception& e)
	{
		LOGE << std::format("Message : {}", e.what());
		return false;
	}
	pcQueryData->SetAdoRecordSet(GetCommandName(), pcAdoRecordSet);
	pcQueryData->SetReturnValue(pcQueryData->GetQuerySetCount() - 1, GetCommandName(), m_iReturnValue);
	ResultHandler(m_iReturnValue);
	return true;
}

bool NSAdoMySQLCommand::SetItem(const char* pFieldName, const char* pValue)
{	
	try
	{
		switch (m_pCommand->GetParameters()->Item[pFieldName]->GetType())
		{
		case adChar:
		case adVarChar:
		case adLongVarChar:
		case adBinary:
		case adVarBinary:
		{
			bool hasUnicode = false;
			for (const unsigned char* p = (const unsigned char*)pValue; *p; ++p) {
				if (*p > 0x7F) {
					hasUnicode = true;
					break;
				}
			}

			if (hasUnicode)
			{
				_variant_t var;
				var.vt = VT_BSTR;

				int wlen = MultiByteToWideChar(CP_UTF8, 0, pValue, -1, NULL, 0);
				if (wlen > 0)
				{
					wchar_t* wstr = new wchar_t[wlen];
					if (MultiByteToWideChar(CP_UTF8, 0, pValue, -1, wstr, wlen))
					{
						var.bstrVal = SysAllocString(wstr);
						m_pCommand->GetParameters()->Item[pFieldName]->PutType(adVarWChar);
						m_pCommand->GetParameters()->Item[pFieldName]->Value = var;
					}
					delete[] wstr;
				}
			}
			else
			{
				BSTR bstrValue = _com_util::ConvertStringToBSTR(pValue);
				m_pCommand->GetParameters()->Item[pFieldName]->Value = bstrValue;
				SysFreeString(bstrValue);
			}			
		}
		break;
		case adWChar:
		case adVarWChar:
		{
			if (strlen(pValue) > ADORECORDSET_UNICODE_BUFFER_SIZE)
			{
				LOGF << std::format("Buffer size overflow. MAX {} bytes. [{}]", ADORECORDSET_UNICODE_BUFFER_SIZE, pFieldName);
				return false;
			}
			MultiByteToWideChar(CP_UTF8, 0, pValue, -1, m_unicode, ADORECORDSET_UNICODE_BUFFER_SIZE);
			BSTR bstr = SysAllocString(m_unicode);
			m_pCommand->GetParameters()->Item[pFieldName]->Value = bstr;
			SysFreeString(bstr);
		}
		break;
		}
	}
	catch (const _com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::SetItem(const char* pFieldName, const bool bValue)
{
	uint8_t uValue = (bValue == true) ? 1 : 0;

	return SetItem(pFieldName, uValue);
}

bool NSAdoMySQLCommand::SetItem(const char* pFieldName, const uint8_t uValue)
{
	try
	{
		m_pCommand->GetParameters()->Item[pFieldName]->Value = uValue;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::SetItem(const char* pFieldName, const uint16_t uValue)
{
	try
	{
		m_pCommand->GetParameters()->Item[pFieldName]->Value = uValue;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::SetItem(const char* pFieldName, const uint32_t uValue)
{
	try
	{
		m_pCommand->GetParameters()->Item[pFieldName]->Value = uValue;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::SetItem(const char* pFieldName, const uint32_t uIndex, const uint16_t uValue)
{
	char strFieldName[128] = {
		0,
	};
	sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

	return SetItem(strFieldName, uValue);
}

bool NSAdoMySQLCommand::SetItem(const char* pFieldName, const uint32_t uIndex, const int iValue)
{
	char strFieldName[128] = {
		0,
	};
	sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

	return SetItem(strFieldName, iValue);
}

bool NSAdoMySQLCommand::SetItem(const char* pFieldName, const uint32_t uIndex, const uint32_t uValue)
{
	char strFieldName[128] = {
		0,
	};
	sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

	return SetItem(strFieldName, uValue);
}

bool NSAdoMySQLCommand::SetItem(const char* pFieldName, const int iValue)
{
	try
	{
		m_pCommand->GetParameters()->Item[pFieldName]->Value = iValue;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::SetItem(const char* pFieldName, const int64_t iValue)
{
	try
	{
		_variant_t vt;
		vt.vt = VT_I8;
		vt.llVal = iValue;
		m_pCommand->GetParameters()->Item[pFieldName]->Value = vt;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::SetItem(const char* pFieldName, const uint64_t uValue)
{
	try
	{
		_variant_t vt;
		vt.vt = VT_I8;
		vt.llVal = uValue;
		m_pCommand->GetParameters()->Item[pFieldName]->Value = vt;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::SetItem(const char* pFieldName, const float fValue)
{
	try
	{
		_variant_t vt;
		vt.vt = VT_R4;
		vt.fltVal = fValue;
		m_pCommand->GetParameters()->Item[pFieldName]->Value = vt;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::SetItem(const char* pFieldName, const uint32_t uIndex, const uint64_t uValue)
{
	char strFieldName[128] = {
		0,
	};
	sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

	return SetItem(strFieldName, uValue);
}

bool NSAdoMySQLCommand::SetItem(const char* pFieldName, _variant_t& value)
{
	try
	{
		m_pCommand->GetParameters()->Item[pFieldName]->Value = value;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::SetItem(const char* pFieldName, SYSTEMTIME& sysTime)
{
	try
	{
		_variant_t vt;
		vt.vt = VT_DATE;
		SystemTimeToVariantTime(&sysTime, &vt.date);
		m_pCommand->GetParameters()->Item[pFieldName]->Value = vt;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::SetItem(const char* pFieldName, NPDateTime& dtTime)
{
	try
	{
		SYSTEMTIME sysTime = NSUtil::ConvertDateTimeToSystemTime(dtTime);

		_variant_t vt;
		vt.vt = VT_DATE;
		SystemTimeToVariantTime(&sysTime, &vt.date);
		m_pCommand->GetParameters()->Item[pFieldName]->Value = vt;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::SetItemBinary(const char* pFieldName, byte* pValue, int iSize)
{
	try
	{
		if (!iSize)
			return true;

		// SAFEARRAY 선언
		SAFEARRAY FAR* psa;
		SAFEARRAYBOUND rgsabound[1];
		rgsabound[0].lLbound = 0;
		rgsabound[0].cElements = iSize;

		// 스트림데이터 저장
		psa = SafeArrayCreateVector(VT_UI1, 1, iSize);

		unsigned char* pData = nullptr;

		//잠긴어레이에서 데이터 포인터를 얻어온다.
		SafeArrayAccessData(psa, (void**)&pData);

		//SAFEARRAY에 데이터 복사
		memcpy(pData, pValue, iSize);

		//SAFEARRAY를 풀어준다.
		SafeArrayUnaccessData(psa);

		_variant_t vt;
		vt.vt = VT_ARRAY | VT_UI1;
		vt.parray = psa;

		m_pCommand->GetParameters()->Item[pFieldName]->Value = vt;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::GetItem(const char* pFieldName, bool& bValue)
{
	uint8_t iValue = 0;
	bool	bResult = GetItem(pFieldName, iValue);

	bValue = (iValue != 0) ? true : false;

	return bResult;
}

bool NSAdoMySQLCommand::GetItem(const char* pFieldName, uint16_t& iValue)
{
	try
	{
		iValue = m_pCommand->GetParameters()->GetItem(pFieldName)->GetValue().uiVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::GetItem(const char* pFieldName, uint32_t& iValue)
{
	try
	{
		iValue = m_pCommand->GetParameters()->GetItem(pFieldName)->GetValue().uintVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::GetItem(const char* pFieldName, uint64_t& iValue)
{
	try
	{
		iValue = m_pCommand->GetParameters()->GetItem(pFieldName)->GetValue().llVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::GetItem(const char* pFieldName, const uint32_t uIndex, uint8_t& iValue)
{
	try
	{
		char strFieldName[128] = {
			0,
		};
		sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

		iValue = m_pCommand->GetParameters()->GetItem(strFieldName)->GetValue().bVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::GetItem(const char* pFieldName, const uint32_t uIndex, uint16_t& iValue)
{
	try
	{
		char strFieldName[128] = {
			0,
		};
		sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

		iValue = m_pCommand->GetParameters()->GetItem(strFieldName)->GetValue().uiVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::GetItem(const char* pFieldName, const uint32_t uIndex, int32_t& iValue)
{
	try
	{
		char strFieldName[128] = {
			0,
		};
		sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

		iValue = m_pCommand->GetParameters()->GetItem(strFieldName)->GetValue().intVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::GetItem(const char* pFieldName, const uint32_t uIndex, uint32_t& iValue)
{
	try
	{
		char strFieldName[128] = {
			0,
		};
		sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

		iValue = m_pCommand->GetParameters()->GetItem(strFieldName)->GetValue().uintVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::GetItem(const char* pFieldName, const uint32_t uIndex, uint64_t& iValue)
{
	try
	{
		char strFieldName[128] = {
			0,
		};
		sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

		iValue = m_pCommand->GetParameters()->GetItem(strFieldName)->GetValue().llVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::GetItem(const char* pFieldName, uint8_t& iValue)
{
	try
	{
		iValue = m_pCommand->GetParameters()->GetItem(pFieldName)->GetValue().bVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::GetItem(const char* pFieldName, int& iValue)
{
	try
	{
		iValue = m_pCommand->GetParameters()->GetItem(pFieldName)->GetValue().intVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::GetItem(const char* pFieldName, int64_t& liValue)
{
	try
	{
		liValue = m_pCommand->GetParameters()->GetItem(pFieldName)->GetValue().llVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::GetItem(const char* pFieldName, float& fValue)
{
	try
	{
		fValue = m_pCommand->GetParameters()->GetItem(pFieldName)->GetValue().fltVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}
	return true;
}

bool NSAdoMySQLCommand::GetItem(const char* pFieldName, NPDateTime& dtTime)
{
	try
	{
		SYSTEMTIME sysTime;

		_variant_t vtSrcValue = m_pCommand->GetParameters()->GetItem(pFieldName)->Value;
		_variant_t vtDstValue;
		if (VariantChangeTypeEx(&vtDstValue, &vtSrcValue, GetUserDefaultLCID(), 0, VT_DATE) != S_OK)
			return false;
		VariantTimeToSystemTime(vtDstValue.date, &sysTime);
		VariantClear(&vtDstValue);
		dtTime.year = sysTime.wYear;
		dtTime.month = sysTime.wMonth;
		dtTime.day = sysTime.wDay;
		dtTime.hour = sysTime.wHour;
		dtTime.minute = sysTime.wMinute;
		dtTime.second = sysTime.wSecond;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}
	return true;
}

bool NSAdoMySQLCommand::GetItem(const char* pFieldName, int8_t& iValue)
{
	try
	{
		iValue = m_pCommand->GetParameters()->GetItem(pFieldName)->GetValue().bVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::GetItem(const char* pFieldName, char* pValue, int iSize)
{
	try
	{
		_ParameterPtr param = m_pCommand->GetParameters()->GetItem(pFieldName);
		if (!param)
			return false;
		
		_variant_t value = param->GetValue();

		if (value.vt == VT_BSTR)
		{
			BSTR bstr = value.bstrVal;
			if (bstr)
			{
				WideCharToMultiByte(CP_UTF8, 0, bstr, -1, pValue, iSize, NULL, NULL);
				return true;
			}
		}
		else if (value.vt != VT_NULL)
		{
			_bstr_t bstr(value);
			strncpy_s(pValue, iSize, (char*)bstr, iSize - 1);
			pValue[iSize - 1] = '\0';
			return true;
		}

		return false;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}
}

bool NSAdoMySQLCommand::GetItemUTF8(const char* pFieldName, char* pValue, int iSize)
{
	try
	{
		DataTypeEnum eDataType = m_pCommand->GetParameters()->GetItem(pFieldName)->GetType();
		if (adBSTR != eDataType)
			return false;
		std::wstring str((LPWSTR)(_bstr_t(m_pCommand->GetParameters()->GetItem(pFieldName)->GetValue().bstrVal)));
		str = str.erase(str.find_last_not_of(L" \t\r\n") + 1);
		WideCharToMultiByte(CP_UTF8, 0, str.c_str(), -1, pValue, iSize - 1, nullptr, nullptr);
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoMySQLCommand::GetItemBinary(char* pData, byte* pValue, int iSize, const char* pFieldName)
{
	try
	{
		if (!iSize)
			return true;

		// SAFEARRAY 선언
		SAFEARRAY FAR* psa;
		SAFEARRAYBOUND rgsabound[1];
		rgsabound[0].lLbound = 0;
		rgsabound[0].cElements = iSize;

		// 스트림데이터 저장
		psa = SafeArrayCreateVector(VT_UI1, 1, iSize);

		//잠긴어레이에서 데이터 포인터를 얻어온다.
		SafeArrayAccessData(psa, (void**)&pData);

		//SAFEARRAY에 데이터 복사
		memcpy(pData, pValue, iSize);

		//SAFEARRAY를 풀어준다.
		SafeArrayUnaccessData(psa);

		//삭제
		SafeArrayDestroyData(psa);
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}
