#pragma once
#include <unordered_map>
#include <queue>
#include <functional>
#include <memory>
#include <atomic>
#include <thread>
#include <mutex>
#include <chrono>
#include <vector>
#include <mysql.h>

class AsyncDBWorker
{
public:
    AsyncDBWorker();
    ~AsyncDBWorker();

    // 초기화/종료
    bool Initialize(int threadIndex, class NSMySQLConnectionPool* pool);
    void Finalize();

    // 작업 타입
    struct WorkItem
    {
        int64_t cid;
        std::string procName;
        std::function<void(bool success, MYSQL_RES* result)> callback;
        std::function<void(MYSQL_STMT*)> bindParams;  // 파라미터 바인딩 함수
        std::chrono::steady_clock::time_point queuedTime;
    };

    // 작업 등록
    void PostWork(WorkItem work);

    // 통계
    int64_t GetPendingCount() const { return m_totalPending.load(); }
    int64_t GetActiveCount() const { return m_activeOps.size(); }

private:
    // CID별 작업 큐
    struct CidQueue
    {
        std::queue<WorkItem> pending;
        bool isExecuting = false;
        MYSQL_STMT* currentStmt = nullptr;
    };

    // 비동기 작업 상태
    struct AsyncOp
    {
        int64_t cid;
        MYSQL_STMT* stmt;
        WorkItem work;
        int status;
        std::chrono::steady_clock::time_point startTime;
        bool resultFetched = false;
        MYSQL_RES* result = nullptr;
    };

    // 메인 처리 루프
    void ProcessLoop();
    
    // 다음 작업 시작
    void StartNextWork(int64_t cid, CidQueue& queue);
    
    // 진행 중인 작업들 폴링
    void PollActiveOperations();
    
    // 완료된 작업 처리
    void ProcessCompletedOperations();

    // PreparedStatement 캐시
    MYSQL_STMT* GetCachedStatement(const std::string& procName);
    void ReleaseCachedStatement(const std::string& procName, MYSQL_STMT* stmt);

private:
    int m_threadIndex = -1;
    NSMySQLConnectionPool* m_pool = nullptr;
    std::unique_ptr<class NSMySQLConnection> m_connection;
    
    // CID별 큐 관리
    std::unordered_map<int64_t, CidQueue> m_cidQueues;
    std::mutex m_queueMutex;
    
    // 진행 중인 비동기 작업
    std::vector<AsyncOp> m_activeOps;
    
    // PreparedStatement 캐시
    std::unordered_map<std::string, std::vector<MYSQL_STMT*>> m_stmtCache;
    std::mutex m_stmtCacheMutex;
    
    // 워커 스레드
    std::thread m_workerThread;
    std::atomic<bool> m_running{false};
    
    // 통계
    std::atomic<int64_t> m_totalPending{0};
    std::atomic<int64_t> m_totalProcessed{0};
};