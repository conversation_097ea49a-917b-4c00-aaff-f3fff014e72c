#pragma once
#include "NSStoredProcedure.h"
#include <vector>
#include <memory>

// 배치 실행을 위한 저장 프로시저 (레거시 호환)
class NSStoredProcedureBatch : public NSStoredProcedure
{
public:
    NSStoredProcedureBatch() = default;
    virtual ~NSStoredProcedureBatch() = default;

    // 프로시저 추가
    void AddStoredProcedure(std::shared_ptr<NSStoredProcedure> procedure)
    {
        if (procedure)
        {
            m_procedures.push_back(procedure);
        }
    }

    // 프로시저 개수
    size_t GetCount() const { return m_procedures.size(); }
    
    // 유효성 검사
    bool IsValid() const { return !m_procedures.empty(); }

    // NSStoredProcedure 인터페이스 구현
    const char* GetName() const override { return "NSStoredProcedureBatch"; }
    
    EErrorCode MakeQuery(MySQLCommand* command) override 
    { 
        // 배치는 개별 프로시저가 각자 처리
        return EErrorCode::Success; 
    }
    
    EErrorCode MakeOutput(MySQLCommand* command) override 
    { 
        // 배치는 개별 프로시저가 각자 처리
        return EErrorCode::Success; 
    }

    // 배치 실행 (트랜잭션으로 묶어서 실행)
    std::shared_ptr<NSQueryData> ExecuteBatch(
        NSMySQLConnection* connection,
        const NSDataSerializer& serializer);

protected:
    std::vector<std::shared_ptr<NSStoredProcedure>> m_procedures;
};