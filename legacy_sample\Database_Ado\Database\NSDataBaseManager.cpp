#include "stdafx.h"

#include "NSDataBaseManager.h"
#include "DataBase/NSConnectionPool.h"
#include "Database/ConnectionPool/NSSQLServerConnectionPool.h"
#include "DataBase/ConnectionPool/NSMySQLConnectionPool.h"

__declspec(thread) std::array<NSAdoConnection*, (int)EDataBase::End> tlsDbConnectionList;

bool NSDataBaseManager::NSDataBaseExecutor::RunningInThisThread() const
{
	return ThreadId == std::this_thread::get_id();
}

void NSDataBaseManager::NSDataBaseExecutor::Post(const std::function<void()>& function)
{
	InterlockedIncrement64(&QueueSize);

	Queue.push(function);
}

NSDataBaseManager::NSDataBaseManager()
{
	//초기화 처리
	for (int i = 0; i < (int)EDataBase::End; i++)
	{
		m_pcAdoConnectionPool[i] = nullptr;
	}
}

NSDataBaseManager::~NSDataBaseManager()
{
	if (m_IsRunning)
	{
		Stop();
	}

	for (int i = 0; i < (int)EDataBase::End; i++)
	{
		if (m_pcAdoConnectionPool[i])
		{
			delete m_pcAdoConnectionPool[i];
		}
	}
}

bool NSDataBaseManager::Start(uint32_t workThreadCnt)
{
	//상태 플레그 셋팅
	m_IsRunning = true;

	for (uint32_t i = 0; i < workThreadCnt; i++)
	{
		m_DataBaseExecutors.push_back(std::make_unique<NSDataBaseExecutor>());
	}

	//쓰레드 생성
	m_WorkThreads = std::vector<NSThread>(workThreadCnt);
	HANDLE hEvent = CreateEvent(NULL, FALSE, FALSE, NULL);
	if (hEvent == NULL)
	{
		LOGE << "CreateEvent Fail";
		return false;
	}
	for (uint32_t i = 0; i < workThreadCnt; i++)
	{
		m_WorkThreads[i] = NSThread([this, i, hEvent]() { this->WorkThreadProc(i, hEvent); });
		WaitForSingleObject(hEvent, INFINITE);
		m_WorkThreads[i].SetDescription(std::format("NSDatabaseWorker#{}", i));
	}
	CloseHandle(hEvent);
	m_WorkThreadCount = workThreadCnt;
	return true;
}

bool NSDataBaseManager::Stop()
{
	LOGI << "Wait WorkThread Stop";

	if (m_IsRunning == false)
		return true;

	m_IsRunning = false;

	std::shared_ptr<NSQueryData> pcQueryData;

	for (auto itr = m_WorkThreads.begin(); itr != m_WorkThreads.end(); ++itr)
	{
		if (itr->IsRunning())
		{
			itr->Wait();
		}
	}

	LOGI << "Stopped WorkThread";
	return true;
}

void NSDataBaseManager::ProhibitPushAccess()
{
	//PushQueryData 에서의 큐 접근을 막는다.
	m_PushAccessProhibit.store(true);

	//아직 PushQueryData 함수 내부에 있는 스레드가 함수 실행을 끝내는 것을 기다린다.
	while (m_PushAccessCnt != 0)
	{
		std::this_thread::sleep_for(std::chrono::milliseconds(1));
	}
}

void NSDataBaseManager::StopAllWorkerThreadAndWait()
{
	//쿼리 수행 스레드를 모두 정지 시킨다.
	m_IsRunning = false;

	for (auto itr = m_WorkThreads.begin(); itr != m_WorkThreads.end(); ++itr)
	{
		if (itr->IsRunning())
		{
			itr->Wait();
		}
	}
}

bool NSDataBaseManager::AddConnectionInfo(EDataBase dbType, const EDBProvider provider, const std::string_view host, uint32_t port, const std::string_view dbName,
	const std::string_view user, const std::string_view password, int32_t initPoolCount)
{
	if (!CheckDbType(dbType))
		return false;

	NSConnectionPool* connectionPool = m_pcAdoConnectionPool[(int)dbType];
	if (connectionPool != nullptr)
	{
		LOGE << "Already Exits";
		return false;
	}

	if (provider == EDBProvider::MSOLEDBSQL)
	{
		connectionPool = new NSSQLServerConnectionPool(host, port, dbName, user, password);
	}
	else if (provider == EDBProvider::MSDASQL)
	{
		connectionPool = new NSMySQLConnectionPool(host, port, dbName, user, password);
	}
	else
	{
		LOGE << "Invalid Provider";
		return false;
	}

	if (!connectionPool->InitPool(initPoolCount, 1, true))
	{
		LOGE << "NSConnectionPool Init Fail!";
		delete connectionPool;
		return false;
	}

	m_pcAdoConnectionPool[(int)dbType] = connectionPool;
	return true;
}

auto NSDataBaseManager::GetDBConnection(EDataBase dbType) const->NSAdoConnection*
{
	if (!CheckDbType(dbType))
		return nullptr;

	auto connection = tlsDbConnectionList[(int)dbType];
	if (connection != nullptr)
	{
		//failover 고려해야 할 수도 있음. 우선은 기존 동작과 동일하게
		if (connection->GetConnection()->GetState() == adStateClosed)
		{
			if (!ReconnectConnection(dbType, connection))
			{
				return nullptr;
			}
		}
	}
	return connection;
}

bool NSDataBaseManager::ReconnectConnection(EDataBase dbType, NSAdoConnection* connection) const
{
	for (int attempt = 0; attempt < MAX_RECONNECT_ATTEMPTS; ++attempt)
	{
		// 새 연결 시도
		NSConnectionPool* pool = m_pcAdoConnectionPool[(int)dbType];
		if (pool == nullptr)
			return false;

		std::ostringstream strConnection_info;
		strConnection_info << "Driver=" << "{MySQL ODBC 9.0 Unicode Driver}" << ";";
		strConnection_info << "server=" << pool->GetHost() << ";";
		strConnection_info << "port=" << pool->GetPort() << ";";
		strConnection_info << "Database=" << pool->GetDBName() << "; ";
		strConnection_info << "User=" << pool->GetUserID() << ";";
		strConnection_info << "Password=" << pool->GetPassword() << ";";
		strConnection_info << "Option=" << "3" << ";";

		// 연결 재시도
		if (connection->Connect(strConnection_info.str().c_str()))
		{
			LOGI << std::format("msg=Successfully reconnected to database on attempt {}", attempt + 1);
			return true;
		}

		LOGW << std::format("Reconnection attempt {} failed: {}", attempt + 1, strConnection_info.str());

		// 마지막 시도가 아니라면 잠시 대기
		if (attempt < MAX_RECONNECT_ATTEMPTS - 1)
		{
			std::this_thread::sleep_for(std::chrono::milliseconds(RECONNECT_DELAY_MS));
		}
	}

	LOGE << "msg=All reconnection attempts failed";
	return false;
}

auto NSDataBaseManager::GetDBQueueSize() const -> int64_t
{
	int64_t totalQueueSize = 0;

	for (const auto& executor : m_DataBaseExecutors)
	{
		totalQueueSize += executor->QueueSize;
	}
	return totalQueueSize;
}

std::string NSDataBaseManager::GetConnectionPoolCountInfo() const
{
	std::ostringstream strInfo;

	for (int iCnt = 0; iCnt < (int)EDataBase::End; ++iCnt)
	{
		auto connectionPool = m_pcAdoConnectionPool[iCnt];
		if (connectionPool == nullptr)
			continue;

		strInfo << "[DBType:" << to_string(static_cast<EDataBase>(iCnt)) << "/Name:" << connectionPool->GetDBName() << "]\t" << connectionPool->GetAllocSize() << "/" << connectionPool->GetSize() << " (Remain/Pool)";

		strInfo << std::endl;
	}

	return strInfo.str();
}

std::string NSDataBaseManager::GetConnectionPoolCountLog() const
{
	std::ostringstream szInfo;

	for (int iCnt = 0; iCnt < (int)EDataBase::End; ++iCnt)
	{
		if (m_pcAdoConnectionPool[iCnt] == nullptr)
			continue;

		szInfo << "#" << m_pcAdoConnectionPool[iCnt]->GetAllocSize(); //[db_type_n_remain]
		szInfo << "#" << m_pcAdoConnectionPool[iCnt]->GetSize();	  //[db_type_n_max]
	}

	return szInfo.str();
}

auto NSDataBaseManager::GetConnectionPool(EDataBase dbType) const -> NSConnectionPool*
{
	return m_pcAdoConnectionPool[static_cast<int>(dbType)];
}

void NSDataBaseManager::SetAfterExecuteQuery(const std::function<void(const std::shared_ptr<NSQueryData>&)>& onAfterExecuteQuery)
{
	m_OnAfterExecuteQuery = onAfterExecuteQuery;
}

bool NSDataBaseManager::CheckDbType(EDataBase dbType) const
{
	if (EDataBase::End <= dbType)
	{
		LOGE << "Please Check DataBase Type";
		return false;
	}

	return true;
}

void NSDataBaseManager::WorkThreadProc(int iThreadIndex, HANDLE hEvent)
{
	std::thread::id tid = std::this_thread::get_id();

	m_DataBaseExecutors[iThreadIndex]->ThreadId = tid;
	Promise::SetCurrentExecutor(tid, m_DataBaseExecutors[iThreadIndex].get());

	SetDBConnectionList();
	SetEvent(hEvent);
	while (m_IsRunning)
	{
		std::function<void()> function;
		while (m_DataBaseExecutors[iThreadIndex]->Queue.try_pop(function))
		{
			InterlockedDecrement64(&m_DataBaseExecutors[iThreadIndex]->QueueSize);
			function();
		}

		//큐가 비어있을때는 잠시 쉬어준다. ( 이벤트로 해야될지는 고민)
		std::this_thread::sleep_for(std::chrono::milliseconds(1));
	}

	ReleaseDBConnectionList();

	Promise::SetCurrentExecutor(tid, nullptr);
}

void NSDataBaseManager::StorageUpdateQuery(
	std::shared_ptr<NSStorageUpdateContainer> containerData,
	std::function<EErrorCode(const std::shared_ptr<NSQueryData>, const std::shared_ptr<NSStorageUpdateContainer>)> pQueryFunc,
	std::function<EErrorCode(const std::shared_ptr<NSQueryData>, const std::shared_ptr<NSStorageUpdateContainer>)> pResultFunc,
	std::shared_ptr<NSDBSession> session,
	std::source_location location)
{
	if (m_PushAccessProhibit.load() == true)
		return;

	m_PushAccessCnt.fetch_add(1);

	auto queryData = std::make_shared<NSQueryData>(location.function_name(), location.line(), session);

	CNLServerInfo::GetInstance()->IncDatabaseQueryPostCount(1);
	m_QueriesProcessingCount.fetch_add(1);
	m_NumInputForLog.fetch_add(1);

	Post(GetExecutorByShardKey(containerData->Cid),
		[this, queryData, containerData, pQueryFunc, onAfterExecuteQuery = m_OnAfterExecuteQuery]()
		{
			NSElapsedTimeUtil::ScopedDuration timer{ queryData, onAfterExecuteQuery };
			queryData->SetErrorCode(pQueryFunc(queryData, containerData));

			if (queryData->GetErrorCode() == EErrorCode::None)
			{
				CNLServerInfo::GetInstance()->IncDatabaseQuerySucceededCount(1);
			}
			else
			{
				CNLServerInfo::GetInstance()->IncDatabaseQueryFailedCount(1);
			}

			m_QueriesProcessingCount.fetch_sub(1);
			m_NumOutputForLog.fetch_add(1);
		})
		.Then([queryData, containerData, pResultFunc]()
			{
				queryData->SetErrorCode(pResultFunc(queryData, containerData));
			});

	m_PushAccessCnt.fetch_sub(1);
}

auto NSDataBaseManager::GetExecutorByShardKey(uint64_t shardKey) -> Promise::NSExecutor&
{
	if (shardKey == 0)
	{
		//랜덤하게 쓰레드 선택
		return *m_DataBaseExecutors[std::rand() % m_WorkThreads.size()];
	}
	else
	{
		//Hashing
		return *m_DataBaseExecutors[shardKey % m_WorkThreads.size()];
	}
}

void NSDataBaseManager::SetDBConnectionList()
{
	for (int i = 0; i < (int)EDataBase::End; ++i)
	{
		if (tlsDbConnectionList[i] == nullptr)
		{
			NSAdoConnection* connection = nullptr;
			// 서버 구동시에 dbConnection은 pop에 실패하면 안된다. 성공할 때까지 루프
			// 동시에 여러 thread에서 connection 추가 생성 요청하더라도 필요한 만큼만 생성하도록 NSPool 수정
			while (true)
			{
				if (!m_pcAdoConnectionPool[i])
					break;
				connection = m_pcAdoConnectionPool[i]->Pop();
				if (connection != nullptr)
					break;

				std::this_thread::sleep_for(std::chrono::milliseconds(100));
			}

			tlsDbConnectionList[i] = connection;
		}
	}
}

void NSDataBaseManager::ReleaseDBConnectionList()
{
	for (int i = 0; i < (int)EDataBase::End; ++i)
	{
		if (tlsDbConnectionList[i] != nullptr)
		{
			if (m_pcAdoConnectionPool[i] != nullptr)
			{
				m_pcAdoConnectionPool[i]->Push(tlsDbConnectionList[i]);
			}
			tlsDbConnectionList[i] = nullptr;
		}
	}
}
