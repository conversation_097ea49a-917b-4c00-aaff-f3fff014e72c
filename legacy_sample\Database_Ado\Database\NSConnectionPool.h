#pragma once

#include "Pool/NSPool.h"
#include "ADO/NSAdoConnection.h"

class NSConnectionPool : public NSPool<NSAdoConnection>
{
public:
	NSConnectionPool(const std::string_view host, uint32_t port,
		const std::string_view dbName, const std::string_view user, const std::string_view password);
	virtual ~NSConnectionPool() = default;

	auto GetHost() const->const char*;
	auto GetPort() const->uint32_t;
	auto GetDBName() const->const char*;
	auto GetUserID() const->const char*;
	auto GetPassword() const->const char*;

private:
	virtual auto Alloc() -> NSAdoConnection* = 0;

protected:
	std::string m_Host;
	uint32_t	m_Port{ 0 };
	std::string m_DBName;
	std::string m_UserID;
	std::string m_Password;
};
