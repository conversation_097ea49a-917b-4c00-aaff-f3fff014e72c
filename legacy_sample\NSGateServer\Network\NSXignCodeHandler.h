#pragma once

#include "NSSingleton.h"
#include "xigncode/zwave_sdk_helper.h"
#define XIGNCODE_PACKET_SIZE 512 // 보안 데이터 전송 단위 크기


class NSXignCodeHandler : public TemplateSingleton<NSXignCodeHandler>
{
public:
	NSXignCodeHandler();
	virtual ~NSXignCodeHandler();
	void InitializeXignCode();
	void ReleaseXignCode();
	IXigncodeServer2Ptr GetServer() const { return m_XignCodeServer; }

private:
	IXigncodeServer2Ptr m_XignCodeServer;
	static xbool XCALL SendProc(xuint64 uid, xpvoid context, xpcch data, xulong datasize);
	static void XCALL DisconnectProc(xuint64 uid, xpvoid context, xint code, xctstr report);
};
