#pragma once

#include "NSSingleton.h"

class NSGateTask
{
public:
	NSGateTask();

	~NSGateTask();

	void Run();
	void Reset();

	int32_t GetId() const;
	uint64_t GetRunTime() const;
	void SetTask(uint64_t executionTime, std::function<void()> function);

private:
	uint64_t					m_executionTime = 0;
	std::function<void()>		m_Function;

	int32_t						m_id;
	static int32_t				sm_seedId;
};

class NSTaskCompare
{
public:
	bool operator()(const NSGateTask* left, const NSGateTask* right) const;
};
