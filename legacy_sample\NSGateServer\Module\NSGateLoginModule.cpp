#include "stdafx.h"
#include "NSGateLoginModule.h"

#include "Network/NSClientSessionManager.h"
#include "DataBase/NSDataBaseManager.h"

#include "Network/NSGatePacketRouter.h"
#include "Network/NSClientSession.h"

#include "DataBase/NSQueryData.h"
#include "DataBase/StoredProcedure/Gate/SpLoginAccount.h"

#include "NPPacket.h"
#include "NPModels.h"
#include "Guid/NSGuidManager.h"

#include "Authentication/Steam/NSSteamAuthentication.h"
#include "../NSPublic/NSUtil/NSRedisUtil.h"
#include "Logger/NSLoggerBuidler.h"

#include "Log/NSActionLog.h"
NSGateLoginModule::NSGateLoginModule()
{
}

void NSGateLoginModule::Init()
{
	NSGateModule::Init();

	RegisterMethod<NPPacketLoginReq>(&NSGateLoginModule::ProcessNPPacketLoginReq);
}

bool NSGateLoginModule::OnAddSession(std::shared_ptr<NSClientSession>)
{
	return true;
}

bool NSGateLoginModule::OnMoveSession(std::shared_ptr<NSClientSession>)
{
	return true;
}

bool NSGateLoginModule::OnRemoveSession(std::shared_ptr<NSClientSession>)
{
	return true;
}

bool NSGateLoginModule::OnDisconnect(std::shared_ptr<NSClientSession>)
{
	return true;
}

bool NSGateLoginModule::SendResultLoginAccount(std::shared_ptr<NSClientSession> session, EErrorCode errorCode)
{
	NPPacketLoginAck packet{};
	packet.SetAID(session->GetAID());
	packet.SetMajor(ProjectVersion::GetMajor());
	packet.SetMinor(ProjectVersion::GetMinor());
	packet.SetRevision(ProjectVersion::GetRevision());
	packet.SetBuildVersion(ProjectVersion::GetBuild());
	packet.SetProtocolChecksum(ProjectVersion::GetProtocolSchemeChecksum());
	packet.SetPacketStructChecksum(ProjectVersion::GetProtocolStructChecksum());
	packet.SetResult(errorCode);

	if (errorCode != EErrorCode::None)
	{
		return session->SendThenClose(packet);
	}

	session->Send(packet);
	return true;
}

template <typename PacketType>
bool NSGateLoginModule::VersionCheck(PacketType packet)
{
	const ServerConfig& config = NSConfigManager::GetConfigs();

	if (config.Common.VersionCheck.ProtocolCheck)
	{
		if (packet->GetProtocolChecksum() != ProjectVersion::GetProtocolSchemeChecksum() ||
			packet->GetPacketStructChecksum() != ProjectVersion::GetProtocolStructChecksum())
			return false;
	}

	if (config.Common.VersionCheck.ClientBuildVerCheck)
	{
		if (packet->GetRevision() < Service::GetInstance()->GetMinClientVer())
			return false;
	}

	return true;
}

void NSGateLoginModule::ProcessNPPacketLoginReq(std::shared_ptr<NSClientSession>& session, NPPacketLoginReq* packet)
{
	//세션체크를 프로토콜보다 우선
	const std::string& worldID = packet->GetWorldID();
	const std::string& sessionToken = packet->GetSessionKey();
	const ENPPlatformType platformType = packet->GetPlatformType();
	const std::string& platformID = packet->GetPlatformID();
	const ENPPlatformType idpType = packet->GetIdentityProviderType();
	const std::string& idpID = packet->GetIdentityProviderID();

	session->SetDummyUserInfo(packet->GetDummyClientInfo());
	session->SetUILanguage(packet->GetUILanguage());

	if (!VersionCheck(packet))
	{
		SendResultLoginAccount(session, EErrorCode::InvalidProtocolVersion);
		LOGE << std::format("Invaild Protocol Version. PID:{} Revision:{}",
			NSUtil::UTF8ToAnsiInMaxSize256(platformID.c_str()), packet->GetRevision());
		return;
	}

	NSRedisUtil::IsSessionActive(worldID, platformID, sessionToken)
		.Then([session, worldID, sessionToken, platformType, platformID, idpType, idpID](std::optional<NSRedisUtil::PlatformSessionInfo> result) mutable
			{
				std::string platformAID = "";
				if (result.has_value())
				{
					if (result->PlatformAID.empty())
					{
						session->SetPlatformAID(platformID);
					}
					else
					{
						platformAID = result->PlatformAID;
						session->SetPlatformLogin(true);
						session->SetPlatformAID(result->PlatformAID);
					}

					session->SetRedisSessionToken(sessionToken);
					session->SetActiveSessionID(platformID);
					session->SetCountry(result->Country);
					session->SetOS(result->OS);
					session->SetOSVersion(result->OSVersion);
					session->SetDeviceInfo(result->DeviceInfo);

					if (result->Market.empty())
					{
						session->SetMarket(to_string(platformType));
					}
					else
					{
						session->SetMarket(result->Market);
					}

					session->SetPlatformType(platformType);
					session->SetPlatformID(platformID);
					session->SetIdentityProviderType(idpType);
					session->SetIdentityProviderID(idpID);
				}
				else if (session->IsDummyUser())
				{
					session->SetActiveSessionID(platformID);
				}
				else
				{
					EErrorCode err = EErrorCode::None;
					LOGE << std::format("Invalid SessionToken. WorldID:{} SessionToken:{}",
						worldID, sessionToken);
					const auto oldSession = NSClientSessionManager::GetInstance()->GetSessionByActiveSessionId(platformID);
					if (oldSession != nullptr)
					{
						//NSRedisUtil::IsSessionActive 리턴값이 널인데 올드세션 있으면 중복로그인
						oldSession->SendSystemNtfThenClose(EErrorCode::DuplicateLogin);
						err = EErrorCode::DuplicateLogin;
					}
					else
					{
						err = EErrorCode::PlatformLoginFail;
					}

					SendResultLoginAccount(session, err);
					return;
				}
				NSClientSessionManager::GetInstance()->UpdateActiveSessionId(session);

				{
					const int64_t aid = NSGuidManager::GetInstance()->GenerateGuid(NSGuidManager::Guid::Account);
					const int32_t wid = Service::GetInstance()->GetWid();

					NSDataSerializer dataSerializer;
					dataSerializer.Create<SpLoginAccount>(aid, wid, platformAID, platformType, platformID, idpType, idpID);
					NSDataBaseManager::GetInstance()->StartQuery<SpLoginAccount>(session, dataSerializer)
						.Then([](const std::shared_ptr<NSQueryData> queryData)
							{
								NSGateLoginModule::ResultLoginAccount(queryData);
							});
				}
			});
}

void NSGateLoginModule::ResultLoginAccount(const std::shared_ptr<NSQueryData> queryData)
{
	std::shared_ptr<NSClientSession> clientSession = queryData->GetSession<NSClientSession>();
	assert(clientSession != nullptr);

	const int64_t sessionID = clientSession->GetSessionId();

	if (NSClientSessionManager::GetInstance()->GetSessionBySessionId(sessionID) == nullptr)
	{
		LOGW << std::format("Connection to the session was lost while ResultLoginAccount(), sessionID[{}]", sessionID);
		return;
	}

	//로그인 실패
	if (!queryData->IsValid())
	{
		SendResultLoginAccount(clientSession, queryData->GetErrorCode());
		return;
	}

	//로그인하면 들어갈 퍼시스턴트가 존재하는지
	if (NSWorldServerManager::GetInstance()->FindWorldServerByType(ENpLib_RegionType::WorldMap) == nullptr)
	{
		SendResultLoginAccount(clientSession, EErrorCode::WorldServerLoginFail);
		return;
	}

	int64_t aid = 0;
	const auto recordSet = queryData->GetAdoRecordSet();
	if (!recordSet->IsEOF())
	{
		recordSet->GetItem("AID", aid);
	}

	if (aid == 0)
	{
		SendResultLoginAccount(clientSession, EErrorCode::DBError);
		LOGE << std::format("Login failed due to 0 AID.");
		return;
	}

	clientSession->SetAID(aid);
	if (!NSClientSessionManager::GetInstance()->UpdateSessionAID(clientSession))
	{
		const auto oldSession = NSClientSessionManager::GetInstance()->GetSessionByAID(aid);
		if (oldSession != nullptr)
		{
			oldSession->SendSystemNtfThenClose(EErrorCode::DuplicateLogin);
		}

		SendResultLoginAccount(clientSession, EErrorCode::DuplicateLogin);
		LOGE << std::format("Duplicate Login. AID:{}, SessionId:{}", aid, clientSession->GetSessionId());
		return;
	}

	NPPacketLoginAck clientAck;
	clientAck.SetAID(clientSession->GetAID());
	clientAck.SetMajor(ProjectVersion::GetMajor());
	clientAck.SetMinor(ProjectVersion::GetMinor());
	clientAck.SetRevision(ProjectVersion::GetRevision());
	clientAck.SetBuildVersion(ProjectVersion::GetBuild());
	clientAck.SetProtocolChecksum(ProjectVersion::GetProtocolSchemeChecksum());
	clientAck.SetPacketStructChecksum(ProjectVersion::GetProtocolStructChecksum());
	clientSession->Send(clientAck);

	NSGatePacketRouter::GetInstance()->MoveSession(clientSession, EGateModule::Character);
	NSActionLog::ConnectUser(clientSession);
}


