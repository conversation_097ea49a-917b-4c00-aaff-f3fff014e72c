#include "NSQueryData.h"
#include "RecordSet.h"
#include "NSDBSession.h"
#include <cstring>

NSQueryData::NSQueryData()
    : m_eErrorCode(EErrorCode::None)
{
    m_pcDataSerializer = new NSDataSerializer();
    // unique_ptr 배열은 기본 생성자로 초기화됨
    memset(m_iReturnValue, 0, sizeof(m_iReturnValue));
}

NSQueryData::NSQueryData(const char* function, uint64_t line,
    std::shared_ptr<NSDBSession> session)
    : m_eErrorCode(EErrorCode::None)
    , m_Function(function)
    , m_Line(line)
    , m_pSession(session)
{
    m_pcDataSerializer = new NSDataSerializer();
    // unique_ptr 배열은 기본 생성자로 초기화됨
    memset(m_iReturnValue, 0, sizeof(m_iReturnValue));
}

NSQueryData::NSQueryData(const wchar_t* functionName, int line,
    const TYPE_QUERY_FUNC& queryFunc,
    const TYPE_RESULT_FUNC& resultFunc,
    std::shared_ptr<NSDBSession> session)
    : m_eErrorCode(EErrorCode::None)
    , m_pAllocFuncName(functionName)
    , m_iAllocLine(line)
    , m_pQueryFunc(queryFunc)
    , m_pResultFunc(resultFunc)
    , m_pSession(session)
{
    m_pcDataSerializer = new NSDataSerializer();
    // unique_ptr 배열은 기본 생성자로 초기화됨
    memset(m_iReturnValue, 0, sizeof(m_iReturnValue));
}

NSQueryData::~NSQueryData()
{
    Reset();
    delete m_pcDataSerializer;
}

void NSQueryData::Reset()
{
    for (int i = 0; i < g_uMaxQueryRecordSetCount; ++i)
    {
        m_pcRecordset[i].reset();
    }
    
    m_mapRecordsetByName.clear();
    m_mapReturnValueByName.clear();
    m_mapCommandNameByIdx.clear();
    m_iQuerySetCount = 0;
    m_eErrorCode = EErrorCode::None;
    m_errorMessage.clear();
    
    if (m_pcDataSerializer)
        m_pcDataSerializer->Reset();
}

RecordSet* NSQueryData::GetRecordSet()
{
    return GetRecordSet(0);
}

RecordSet* NSQueryData::GetRecordSet(int iIndex)
{
    if (iIndex < 0 || iIndex >= g_uMaxQueryRecordSetCount)
        return nullptr;
    return m_pcRecordset[iIndex].get();
}

RecordSet* NSQueryData::GetRecordSet(const char* strCommandName)
{
    auto it = m_mapRecordsetByName.find(strCommandName);
    if (it != m_mapRecordsetByName.end())
        return it->second;
    return nullptr;
}

void NSQueryData::RunProcQuery()
{
    if (m_pQueryFunc)
    {
        m_eErrorCode = m_pQueryFunc(shared_from_this());
    }
}

void NSQueryData::RunResultQuery()
{
    if (m_pResultFunc)
    {
        m_pResultFunc(shared_from_this());
    }
}

void NSQueryData::SetRecordSet(std::string strCommandName, std::unique_ptr<RecordSet> pcRecordSet)
{
    if (m_iQuerySetCount >= g_uMaxQueryRecordSetCount)
        return;
        
    RecordSet* rawPtr = pcRecordSet.get();  // 참조용 raw pointer
    m_pcRecordset[m_iQuerySetCount] = std::move(pcRecordSet);
    m_mapRecordsetByName[strCommandName] = rawPtr;  // map에는 참조만 저장
    m_mapCommandNameByIdx[m_iQuerySetCount] = strCommandName;
    m_iQuerySetCount++;
}

void NSQueryData::SetReturnValue(int iIndex, std::string strCommandName, int iReturnValue)
{
    if (iIndex < 0 || iIndex >= g_uMaxQueryRecordSetCount)
        return;
        
    m_iReturnValue[iIndex] = iReturnValue;
    m_mapReturnValueByName[strCommandName] = iReturnValue;
}

const char* NSQueryData::GetCommandName(int iIndex)
{
    auto it = m_mapCommandNameByIdx.find(iIndex);
    if (it != m_mapCommandNameByIdx.end())
        return it->second.c_str();
    return nullptr;
}

int NSQueryData::GetReturnValue()
{
    return GetReturnValue(0);
}

int NSQueryData::GetReturnValue(int iIndex)
{
    if (iIndex < 0 || iIndex >= g_uMaxQueryRecordSetCount)
        return -1;
    return m_iReturnValue[iIndex];
}

int NSQueryData::GetReturnValue(const char* strCommandName)
{
    auto it = m_mapReturnValueByName.find(strCommandName);
    if (it != m_mapReturnValueByName.end())
        return it->second;
    return -1;
}

void NSQueryData::SetRecordSet(std::unique_ptr<RecordSet> recordSet)
{
    if (m_iQuerySetCount >= g_uMaxQueryRecordSetCount)
        return;
        
    // unique_ptr로 직접 저장 (RAII 패턴 유지)
    m_pcRecordset[m_iQuerySetCount] = std::move(recordSet);
    m_iQuerySetCount++;
}

void NSQueryData::SetError(int errorCode, const std::string& errorMsg)
{
    m_eErrorCode = static_cast<EErrorCode>(errorCode);
    m_errorMessage = errorMsg;
}