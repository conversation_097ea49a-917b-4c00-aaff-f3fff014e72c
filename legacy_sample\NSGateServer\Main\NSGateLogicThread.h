#pragma once

#include "NSSingleton.h"
#include "Promise/NSPromise.h"
#include "Win32/NSThread.h"

#include "Network/INetworkHandler.h"

class NSGateLogicThread : public TemplateSingleton<NSGateLogicThread>, public Promise::NSExecutor
{
public:
	NSGateLogicThread();
	~NSGateLogicThread() override = default;

	void Process();

	void ProcessCommunityServerTryConnect();
	void ProcessExecutor();

	void Start();
	void Stop();

	void AddHandler(INetworkHandler* addHandler);

	uint64_t GetCurrentTick() const;

	bool RunningInThisThread() const override;
	void Post(const std::function<void()>& function) override;

private:
	std::atomic<bool> m_Running = false;
	NSThread m_MainThread;
	std::thread::id m_ThreadId;

	std::vector<INetworkHandler*> m_ProcessHandlerList;

	uint64_t m_CurrentTick = 0;
	uint64_t m_LastelElapsedTick = 0;

	std::mutex m_ExecutorMutex;
	std::vector<std::function<void()>> m_ExecutorFrontQueue;
	std::vector<std::function<void()>> m_ExecutorBackQueue;
};
