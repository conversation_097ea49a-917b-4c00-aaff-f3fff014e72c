#pragma once
#include <unordered_map>
#include <concrt.h>
#include <fstream>
#include <vector>
#include <string>
#include <thread>
#include <cstdint>
#include <cstring>
#include <stdlib.h>
#include <ranges>
#include <concurrent_queue.h>

#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#ifndef NOMINMAX
#define NOMINMAX
#endif
#include <windows.h>

#include <WS2tcpip.h>
#include <MSWSock.h>

#include <mmsystem.h>

#pragma warning(push)
#pragma warning(disable:26495 26800 26819 28020)
#include <nlohmann/json.hpp>
#pragma warning(pop)

#pragma warning(push)
#pragma warning(disable:4996 5054 26495 26800)
#define RAPIDJSON_NO_SIZETYPEDEFINE
namespace rapidjson { typedef ::std::size_t SizeType; }

#include <Logger/NSLogger.h>
#include <Json/NSJsonLogObj.h>
#include <Json/NSJsonLogger.h>
#include <KGLogger/NSKGLogger.h>
#include <Profiler/NSProfileLogObj.h>

constexpr int64_t LOG_ID_KG_ACTION = 40;
constexpr int64_t LOG_ID_KG_PERFORMANCE = 43;
constexpr int64_t LOG_ID_KG_DB_QUERY_EXECUTION = 44;

#include <rapidjson/document.h>
#include <rapidjson/writer.h>
#include <rapidjson/stringbuffer.h>
#pragma warning(pop)

#include "System/Bootstrap.h"
#include "System/System.h"
#include "System/Service.h"
#include "System/Config/ServerConfig.h"

#include "Config/NSConfigManager.h"
using NSConfigManager = NSConfigManagerTemplate<ServerConfig>;

#include "NSModels/NSModels.h"
#include <NSLogDefine.h>
constexpr int64_t LOG_ID_DB_QUERY_PERFORMANCE = 5;
constexpr int64_t LOG_ID_DB_QUERY_EXECUTION = 6;

#include "NSUtil/NSUtil.h"
#include "json11.hpp"
#include "NSSingleton.h"

namespace fs = std::filesystem;
