#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpSelectMaintenance : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spSelectMaintenance";
	static constexpr EDataBase procedureHost = EDataBase::Common;
	EErrorCode MakeQuery(NSAdoCommand* command);

public:
	struct Input
	{
		int32_t Wid = 0;
	} Input;

	SpSelectMaintenance() = default;
	SpSelectMaintenance(const int32_t wid);
};