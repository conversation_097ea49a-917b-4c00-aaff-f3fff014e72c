#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpDeleteCharacterWorld : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spDeleteCharacterWorld";
	static constexpr EDataBase procedureHost = EDataBase::Common;
	EErrorCode MakeQuery(NSAdoCommand* command);

public:
	struct Input
	{
		int32_t Wid = 0;
		char PlatformID[g_uMaxPlatformIDLength]{ 0 };
		int32_t DeleteSec;
	} Input;

	SpDeleteCharacterWorld() = default;
	SpDeleteCharacterWorld(const int32_t wid,const std::string_view platformId, const int32_t deleteSec);
};