#pragma once
#include "NSPromise.h"

#if _MSVC_LANG >= 202002L
#include <coroutine>

namespace std
{
	template <typename T, typename... Whatever>
	struct coroutine_traits<NSPromise<T>, Whatever...>
	{
		struct promise_type
		{
			auto get_return_object() -> NSPromise<T>;
			auto initial_suspend() -> std::suspend_never;
			auto final_suspend() noexcept -> std::suspend_never;
			void return_value(T&& value);
			void return_value(const T& value);
			void unhandled_exception();

			NSCompletionToken<T> CompletionToken;
		};
	};

	template <typename... Whatever>
	struct coroutine_traits<NSPromise<void>, Whatever...>
	{
		struct promise_type
		{
			auto get_return_object() -> NSPromise<void>;
			auto initial_suspend() -> std::suspend_never;
			auto final_suspend() noexcept -> std::suspend_never;
			void return_void();
			void unhandled_exception();

			NSCompletionToken<void> CompletionToken;
		};
	};
}


template <typename T>
struct NSPromiseAwaiter
{
	bool await_ready() const;
	void await_suspend(std::coroutine_handle<> handle);
	decltype(auto) await_resume();

	NSPromise<T> Promise;
};

template <typename T>
decltype(auto) operator co_await(NSPromise<T>&& promise)
{
	return NSPromiseAwaiter<T>{
		.Promise = std::move(promise),
	};
}

template <typename T>
decltype(auto) operator co_await(const NSPromise<T>& promise)
{
	return NSPromiseAwaiter<T>{
		.Promise = promise,
	};
}

template <typename T, typename ... Whatever>
auto std::coroutine_traits<NSPromise<T>, Whatever...>::promise_type::get_return_object() -> NSPromise<T>
{
	return NSPromise<T>(CompletionToken);
}

template <typename T, typename ... Whatever>
auto std::coroutine_traits<NSPromise<T>, Whatever...>::promise_type::initial_suspend() -> std::suspend_never
{
	return std::suspend_never{};
}

template <typename T, typename ... Whatever>
auto std::coroutine_traits<NSPromise<T>, Whatever...>::promise_type::final_suspend() noexcept -> std::suspend_never
{
	return std::suspend_never{};
}

template <typename T, typename ... Whatever>
void std::coroutine_traits<NSPromise<T>, Whatever...>::promise_type::return_value(T&& value)
{
	CompletionToken.Set(std::move(value));
}

template <typename T, typename ... Whatever>
void std::coroutine_traits<NSPromise<T>, Whatever...>::promise_type::return_value(const T& value)
{
	CompletionToken.Set(value);
}

template <typename T, typename ... Whatever>
void std::coroutine_traits<NSPromise<T>, Whatever...>::promise_type::unhandled_exception()
{
	std::rethrow_exception(std::current_exception());
}

template <typename ... Whatever>
auto std::coroutine_traits<NSPromise<void>, Whatever...>::promise_type::get_return_object() -> NSPromise<void>
{
	return NSPromise<void>(CompletionToken);
}

template <typename ... Whatever>
auto std::coroutine_traits<NSPromise<void>, Whatever...>::promise_type::initial_suspend() -> std::suspend_never
{
	return std::suspend_never{};
}

template <typename ... Whatever>
auto std::coroutine_traits<NSPromise<void>, Whatever...>::promise_type::final_suspend() noexcept -> std::suspend_never
{
	return std::suspend_never{};
}

template <typename ... Whatever>
void std::coroutine_traits<NSPromise<void>, Whatever...>::promise_type::return_void()
{
	CompletionToken.Set();
}

template <typename ... Whatever>
void std::coroutine_traits<NSPromise<void>, Whatever...>::promise_type::unhandled_exception()
{
	std::rethrow_exception(std::current_exception());
}

template <typename T>
bool NSPromiseAwaiter<T>::await_ready() const
{
	return Promise.IsDone();
}

template <typename T>
void NSPromiseAwaiter<T>::await_suspend(std::coroutine_handle<> handle)
{
	Promise.ContinuationWith([handle]([[maybe_unused]] NSPromise<T>& self)
		{
			handle.resume();
		});
}

template <typename T>
decltype(auto) NSPromiseAwaiter<T>::await_resume()
{
	return Promise.Get();
}
#endif
