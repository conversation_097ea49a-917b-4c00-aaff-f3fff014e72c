#include "CIDQueueManager.h"
#include "NSDataSerializer.h"
#include "NSQueryData.h"
#include "NSMySQLConnection.h"
#include "Threading/ThreadedWorkManager.h"
#include "ConnectionManager.h"
#include "StoredProcedure/NSStoredProcedure.h"
#include "Diagnostics/NSLogger.h"
#include "NSDefine.h"

namespace Database
{

QueryTimer::QueryTimer(std::shared_ptr<NSQueryData> data, 
                       std::function<void(const std::shared_ptr<NSQueryData>&)> callback)
    : m_start(std::chrono::high_resolution_clock::now())
    , m_queryData(std::move(data))
    , m_callback(std::move(callback))
{
}

QueryTimer::~QueryTimer()
{
    auto elapsed = std::chrono::high_resolution_clock::now() - m_start;
    auto elapsedMs = std::chrono::duration_cast<std::chrono::milliseconds>(elapsed).count();
    
    if (m_queryData)
    {
        m_queryData->SetElapsedTime(elapsedMs);
        
        if (m_callback)
        {
            try
            {
                m_callback(m_queryData);
            }
            catch (const std::exception& e)
            {
                LOGE << "Exception in QueryTimer callback: " << e.what();
            }
        }
    }
}

CIDQueueManager::CIDQueueManager()
{
}

CIDQueueManager::~CIDQueueManager()
{
    LOGD << "CIDQueueManager destroyed. Total queries processed: " << m_totalQueriesProcessed.load()
         << ", Total enqueued: " << m_totalQueriesEnqueued.load();
}

void CIDQueueManager::Initialize(ThreadedWorkManager* workerManager,
                                ConnectionManager* connectionManager,
                                std::function<void(const std::shared_ptr<NSQueryData>&)> afterExecuteCallback)
{
    m_workerManager = workerManager;
    m_connectionManager = connectionManager;
    m_afterExecuteCallback = std::move(afterExecuteCallback);
}

void CIDQueueManager::EnqueueQuery(int64_t cid, QueryTask task)
{
    if (!m_workerManager || !m_connectionManager)
    {
        LOGE << "CIDQueueManager not properly initialized";
        task.promise.SetException(std::make_exception_ptr(
            std::runtime_error("CIDQueueManager not initialized")));
        return;
    }

    task.cid = cid;
    m_totalQueriesEnqueued.fetch_add(1);

    auto cidQueue = GetOrCreateQueue(cid);
    
    bool shouldStartProcessing = false;
    {
        std::lock_guard<std::mutex> lock(cidQueue->mutex);
        bool wasEmpty = cidQueue->queries.empty();
        cidQueue->queries.push(std::move(task));
        
        if (wasEmpty && !cidQueue->isProcessing.exchange(true))
        {
            shouldStartProcessing = true;
        }
    }
    
    if (shouldStartProcessing)
    {
        m_workerManager->PostWork([this, cid]() {
            ProcessQueue(cid);
        });
    }
}

std::shared_ptr<CIDQueueManager::CIDQueue> CIDQueueManager::GetOrCreateQueue(int64_t cid)
{
    {
        std::shared_lock<std::shared_mutex> readLock(m_mapMutex);
        auto it = m_cidQueues.find(cid);
        if (it != m_cidQueues.end())
        {
            return it->second;
        }
    }
    
    std::unique_lock<std::shared_mutex> writeLock(m_mapMutex);
    auto it = m_cidQueues.find(cid);
    if (it != m_cidQueues.end())
    {
        return it->second;
    }
    
    auto newQueue = std::make_shared<CIDQueue>();
    m_cidQueues[cid] = newQueue;
    return newQueue;
}

void CIDQueueManager::ProcessQueue(int64_t cid)
{
    auto cidQueueIt = m_cidQueues.end();
    {
        std::shared_lock<std::shared_mutex> readLock(m_mapMutex);
        cidQueueIt = m_cidQueues.find(cid);
        if (cidQueueIt == m_cidQueues.end())
        {
            LOGE << "CID queue not found for CID: " << cid;
            return;
        }
    }
    
    auto cidQueue = cidQueueIt->second;
    
    while (true)
    {
        QueryTask task;
        {
            std::lock_guard<std::mutex> lock(cidQueue->mutex);
            if (cidQueue->queries.empty())
            {
                cidQueue->isProcessing.store(false);
                
                // Double check to avoid race condition
                if (cidQueue->queries.empty())
                {
                    break;
                }
                
                // Race condition detected, restart processing
                if (!cidQueue->isProcessing.exchange(true))
                {
                    continue;
                }
            }
            
            task = std::move(cidQueue->queries.front());
            cidQueue->queries.pop();
        }
        
        try
        {
            ExecuteQuery(task);
            m_totalQueriesProcessed.fetch_add(1);
        }
        catch (const std::exception& e)
        {
            LOGE << "Exception executing query for CID " << cid << ": " << e.what();
            task.promise.SetException(std::current_exception());
        }
    }
}

void CIDQueueManager::ExecuteQuery(const QueryTask& task)
{
    auto conn = m_connectionManager->GetNextConnection();
    if (!conn)
    {
        throw std::runtime_error("Failed to get database connection");
    }
    
    // RAII pattern for timing and callback
    {
        QueryTimer timer(task.queryData, m_afterExecuteCallback);
        
        // Execute the stored procedure using the provided function
        if (task.executeFunc)
        {
            task.executeFunc(conn.get(), task.serializer, task.queryData);
        }
        else
        {
            LOGE << "No execute function provided for procedure: " << task.procedureName;
            task.queryData->SetErrorCode(EErrorCode::InvalidParameter);
        }
    }
    
    // Set the promise value to notify the waiting code
    task.promise.SetValue(task.queryData);
}

int64_t CIDQueueManager::ExtractCIDFromSerializer(const NSDataSerializer& serializer)
{
    // This is a placeholder - actual implementation would extract CID from serializer
    // based on the specific protocol/format used
    // For now, assuming CID is the first int64_t parameter
    int64_t cid = 0;
    // serializer.Read(cid); // Pseudo-code
    return cid;
}

size_t CIDQueueManager::GetQueueCount() const
{
    std::shared_lock<std::shared_mutex> lock(m_mapMutex);
    return m_cidQueues.size();
}

size_t CIDQueueManager::GetQueueSize(int64_t cid) const
{
    std::shared_lock<std::shared_mutex> readLock(m_mapMutex);
    auto it = m_cidQueues.find(cid);
    if (it != m_cidQueues.end())
    {
        std::lock_guard<std::mutex> queueLock(it->second->mutex);
        return it->second->queries.size();
    }
    return 0;
}

void CIDQueueManager::GetQueueStats(std::unordered_map<int64_t, size_t>& stats) const
{
    stats.clear();
    std::shared_lock<std::shared_mutex> readLock(m_mapMutex);
    
    for (const auto& [cid, queue] : m_cidQueues)
    {
        std::lock_guard<std::mutex> queueLock(queue->mutex);
        if (!queue->queries.empty())
        {
            stats[cid] = queue->queries.size();
        }
    }
}

} // namespace Database