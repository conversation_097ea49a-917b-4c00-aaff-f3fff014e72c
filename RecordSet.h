#pragma once
#include <mysql.h>
#include <string>
#include <memory>
#include <unordered_map>
#include <vector>
#include "MySQLCompatibility.h"

// 심플한 RecordSet (레거시 호환, IsEOF에서 자동 MoveNext)
class RecordSet
{
public:
    // MySQL 일반 쿼리용
    RecordSet(MYSQL* mysql, MYSQL_RES* result);
    
    // MySQL PreparedStatement용
    RecordSet(MYSQL_STMT* stmt, MYSQL_RES* metadata);
    
    ~RecordSet();

    // 레거시 호환 인터페이스
    bool IsEOF(); // 자동으로 다음 행으로 이동
    
    // 필드 값 가져오기
    bool GetItem(const std::string& fieldName, int32_t& value);
    bool GetItem(const std::string& fieldName, int64_t& value);
    bool GetItem(const std::string& fieldName, float& value);
    bool GetItem(const std::string& fieldName, double& value);
    bool GetItem(const std::string& fieldName, std::string& value);
    bool GetItem(const std::string& fieldName, std::vector<uint8_t>& value);

    // 필드 인덱스로 가져오기
    bool GetItem(int fieldIndex, int32_t& value);
    bool GetItem(int fieldIndex, int64_t& value);
    bool GetItem(int fieldIndex, float& value);
    bool GetItem(int fieldIndex, double& value);
    bool GetItem(int fieldIndex, std::string& value);
    bool GetItem(int fieldIndex, std::vector<uint8_t>& value);

    // 필드 정보
    int GetFieldCount() const { return m_fieldCount; }
    const char* GetFieldName(int index) const;

private:
    // 다음 행으로 이동 (내부용)
    bool MoveNext();
    
    // 필드 이름으로 인덱스 찾기
    int GetFieldIndex(const std::string& fieldName);
    
    // 필드 매핑 초기화
    void InitFieldMapping();

private:
    MYSQL* m_mysql = nullptr;
    MYSQL_STMT* m_stmt = nullptr;
    std::unique_ptr<MYSQL_RES, decltype(&mysql_free_result)> m_result{nullptr, mysql_free_result};
    MYSQL_ROW m_currentRow = nullptr;
    
    // Statement용 바인딩
    std::unique_ptr<MYSQL_BIND[]> m_binds;
    std::unique_ptr<char[]> m_bindBuffer;
    std::unique_ptr<unsigned long[]> m_lengths;
    std::unique_ptr<mysql_bool_compat[]> m_isNull;
    
    int m_fieldCount = 0;
    bool m_isEof = false;
    bool m_firstCall = true;
    
    // 필드 이름 매핑
    std::unordered_map<std::string, int> m_fieldMapping;
};