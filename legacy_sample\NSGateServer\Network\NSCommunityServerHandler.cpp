#include "stdafx.h"
#include "NSCommunityServerHandler.h"
#include "NSClientSessionManager.h"

#include "Packet/NSPacketPool.h"
#include "NPPacket.h"
#include "NSUtil/NSTokenizer.h"
#include "ServerMain/NSServerMain.h"
#include "NSWorldServerSessionManager.h"

#include "NSModels/NSModels.h"

NSCommunityServerHandler::NSCommunityServerHandler()
{
	m_State = { EState::DisConnect };

	m_State.configure(EState::DisConnect)
		.permit(Trigger::TryConnect, EState::TryConnect);

	m_State.configure(EState::TryConnect)
		.permit(Trigger::DisConnect, EState::DisConnect)
		.permit(Trigger::Connected, EState::Connected);

	m_State.configure(EState::Connected)
		.permit(Trigger::DisConnect, EState::DisConnect);
}

NSCommunityServerHandler::~NSCommunityServerHandler()
{
	//NSPacketData* packetData = nullptr;
	for (NSPacketData* packetData : m_cPacketQueue)
	{
		if (packetData != nullptr)
		{
			NSPacketPool::GetInstance()->ReceiveDeAlloc(packetData);
		}
		else
		{
			LOGE << std::format("PackData is nullptr");
		}
	}
	m_cPacketQueue.clear();
}

void NSCommunityServerHandler::OnClose(int64_t socketChannelId, const std::string& error)
{
	LOGD << std::format("OnClose SocketChannelID: {} ({})", socketChannelId, error);

	std::shared_ptr<NSCommunityServerSession> session = GetSession(socketChannelId);
	if (session != nullptr)
	{
		LOGE << std::format("msg=Connection to the server has been lost, SessionType=CommunityServer, SocketChannelID={}, IP={}, reason={}"
			, socketChannelId, session->GetChannel()->GetIpAddress(), error);
	}
	NSWorldServerManager::GetInstance()->NtfCommunityServerState(false);

	m_CloseSessionQueue.push(socketChannelId);
}

void NSCommunityServerHandler::Process()
{
	//IO 관련 처리는 자동으로 Dispatch_Net_Events에서 자동으로 일어납니다.
	Dispatch_Net_Events();
	ProcessConnect();
	ProcessRecv();
	ProcessClose();
}

void NSCommunityServerHandler::Init()
{

}

void NSCommunityServerHandler::ProcessRecv()
{
	//커뮤니티 서버로부터 들어온 패킷을 릴레이한다.

	//NSPacketData* pcBuffer = nullptr;
	for (NSPacketData* pcBuffer : m_cPacketQueue)
	{
		if (pcBuffer == nullptr)
		{
			assert(false);
			return;
		}

		NSPacket* pNSPacket = (NSPacket*)pcBuffer->GetBuffer();
		int64_t gateSessionId = pNSPacket->GetSessionId();
		int64_t destEndpointSID = pNSPacket->GetDestEndPointSID();
		uint16_t packetType = pNSPacket->GetPacketType();

		LOGI << std::format("ProcessRecv : PacketType : [{}]", static_cast<int32_t>(packetType));

		//서버간 릴레이!!
		//지금은 게임서버 밖에 없음. 
		if (EServerPacketType::Begin < packetType && packetType < EServerPacketType::End)
		{
			LOGI << "Gate <-> CommunityServer Packet";

			switch (pNSPacket->GetPacketDestination())
			{
			case EServerPacketDestination::Game:
			{
				//destination sid로 world server 객체를 특정할 수 있는 경우 이를 통해 패킷을 릴레이.
				std::shared_ptr<NSWorldServer> pSpecifiedServer = NSWorldServerManager::GetInstance()->GetWorldServer(destEndpointSID);
				if (pSpecifiedServer != nullptr)
				{
					pSpecifiedServer->SendNoSpecifySession(*pNSPacket);
					break;
				}

				//sessionId 로 world server 객체를 특정 할 수 있는 경우 이를 통해 패킷을 릴레이.
				//클라이언트 접속 정보를 구한 뒤
				//해당 클라이언트가 접속해 있는 게임서버로 패킷을 보낸다.
				std::shared_ptr<NSClientSession> clientSession =
					NSClientSessionManager::GetInstance()->GetSessionBySessionId(gateSessionId);

				if (clientSession != nullptr)
				{
					clientSession->WorldServerSend(pcBuffer->GetBuffer(), pcBuffer->GetSize());
					break;
				}


				break;
			}

			default:
			{
				LOGI << "Invalid packet destination";
				break;
			}

			}
		}

		else
		{
			//클라이언트로 나가는거
			NPPacket* pcNPPacket = (NPPacket*)pcBuffer->GetBuffer();
			auto pcSession = NSClientSessionManager::GetInstance()->GetSessionBySessionId(pcNPPacket->GetGateSessionid());
			if (pcSession != nullptr)
			{
				LOGI << "Client <-> Gate <-> CommunityServer Packet";
				pcSession->BypassSend(pcBuffer->GetBuffer());
			}
		}
	}

	for (NSPacketData* pcBuffer : m_cPacketQueue)
		NSPacketPool::GetInstance()->ReceiveDeAlloc(pcBuffer);

	m_cPacketQueue.clear();

}

void NSCommunityServerHandler::ProcessConnect()
{
	std::shared_ptr<NSCommunityServerSession> session = nullptr;
	while (m_ConnectQueue.try_pop(session))
	{
		if (session == nullptr)
			return;

		m_SessionList.push_back(session);
		NSWorldServerManager::GetInstance()->NtfCommunityServerState(true);
	}
}

void NSCommunityServerHandler::ProcessClose()
{
	int64_t channelId = 0;
	while (m_CloseSessionQueue.try_pop(channelId))
	{
		auto session = GetSession(channelId);
		if (session)
		{
			if (session->GetChannel())
			{
				session->GetChannel()->PostCloseTask();
			}
		}

		RemoveSession(channelId);

		TryFire(EState::DisConnect);
	}
}

std::shared_ptr<NSCommunityServerSession> NSCommunityServerHandler::GetSession(int64_t sessionId)
{
	if (m_SessionList.empty())
		return nullptr;

	const int64_t index = sessionId % m_SessionList.size();
	return m_SessionList[index];
}

void NSCommunityServerHandler::RemoveSession(int64_t sessionId)
{
	if (m_SessionList.empty())
		return;

	//const int32_t index = channelId % m_SessionList.size();
	auto remove = [sessionId](const auto& element) -> bool {
		if (element->GetSocketChannelId() == sessionId)
			return true;
		else
			return false;
		};
	m_SessionList.erase(std::ranges::remove_if(m_SessionList, remove).begin(), m_SessionList.end());
}

bool NSCommunityServerHandler::Send(const char* buffer, int32_t size, int64_t channelId)
{
	auto session = GetSession(channelId);
	if (session == nullptr)
		return false;

	session->Send(buffer, size);
	return true;
}

void NSCommunityServerHandler::ForwardedEventRecvPacket(int64_t socketInterfaceId, const char* buffer, int32_t size, bool allocateForCopy)
{
	//어댑터에서 호출 될 경우 콜스택이 다음과 같습니다.
	//NSCommunityServerHandler::ForwardedEventRecvPacket <- we are here
	//NSCommunityServerServiceLogicAdapter::ForwardRecvPacketEventIntoThis
	//CNLIOInterfaceAdapter::OnAccept
	//CNLNetEventQueue::Dispatch_Queued_Socket_Accepted
	//CNLNetEventQueue::Dispatch_Net_Events
	//NSClientHandler::Process
	//OnReceive 가 하던 역할을 어댑터 함수가 처리합니다.

	if (buffer == nullptr)
	{
		LOGE << "PacketBuffer IS NULL";
		return;
	}

	NSPacketData* packetData = nullptr;
	if (allocateForCopy)
	{
		packetData = NSPacketPool::GetInstance()->ReceiveAlloc(size);
		if (packetData == nullptr)
		{
			LOGE << "PacketData IS NULL";
			return;
		}

		if (packetData->GetSize() < size)
		{
			NSPacketPool::GetInstance()->ReceiveDeAlloc(packetData);
			LOGE << "PacketData Size Over";
			return;
		}

		memcpy(packetData->GetBuffer(), buffer, size);
	}
	else
	{
		//param buffer 가 NetEventBuffer 의 영역인 경우
		//해당 영역은 서비스로직에서 다음 틱이 시작될 때까지 안전합니다.
		//NSPacketData 객체만 할당하고, 스트림을 복사하지 않아도 됩니다.
		packetData = new NSPacketData(buffer, size, true, true);
		if (packetData == nullptr)
		{
			LOGE << "Allocate for NSPacketData(" << sizeof(NSPacketData) << ") byte failed";
			return;
		}

	}

	packetData->SetSocketChannelID(static_cast<int64_t>(socketInterfaceId));
	m_cPacketQueue.push_back(packetData);
}
void NSCommunityServerHandler::ForwardedEventClose(int64_t socketInterfaceId, [[maybe_unused]] int32_t nativeErrorCode, const std::string& why)
{
	//어댑터에서 호출 될 경우 콜스택이 다음과 같습니다.
	//NSCommunityServerHandler::ForwardedEventClose <- we are here
	//NSCommunityServerServiceLogicAdapter::ForwardCloseEventIntoThis
	//CNLIOInterfaceAdapter::OnDisconnected
	//CNLNetEventQueue::Dispatch_Queued_Socket_Disconnected
	//CNLNetEventQueue::Dispatch_Net_Events
	//NSClientHandler::Process
	//OnClose는 그대로 사용되기 때문에, 인자를 맞춰서 전달합니다.

	OnClose(socketInterfaceId, why.c_str());
}
void NSCommunityServerHandler::ForwardedEventConnectSuccess(const std::shared_ptr<CNLIOInterfaceAdapter>& socketInterface, [[maybe_unused]] const std::string& ip, [[maybe_unused]] unsigned short port)
{
	//어댑터에서 호출 될 경우 콜스택이 다음과 같습니다.
	//NSCommunityServerHandler::ForwardedEventConnectSuccess <- we are here
	//NSCommunityServerServiceLogicAdapter::ForwardConnectSuccessEventIntoThis
	//CNLIOInterfaceAdapter::OnConnectSucceeded
	//CNLNetEventQueue::Dispatch_Queued_Socket_Connect_Succeeded
	//CNLNetEventQueue::Dispatch_Net_Events
	//NSClientHandler::Process
	//NSCommunityServerHandler::ChannelInit 작업을 여기서 진행합니다.

	std::shared_ptr<NSCommunityServerSession> session = std::make_shared<NSCommunityServerSession>();
	session->SetChannel(socketInterface);

	m_ConnectQueue.push(std::move(session));

	LOGI << std::format("msg=Connection to the server was successful, SessionType=CommunityServer, SocketChannelID={}, IP={}", socketInterface->GetChannelID(), ip);

	if (m_bInitChannel)
		return;

	m_bInitChannel = true;

}
void NSCommunityServerHandler::ForwardedEventConnectFail([[maybe_unused]] const std::shared_ptr<CNLIOInterfaceAdapter>& socketInterface, [[maybe_unused]] const std::string& ip, [[maybe_unused]] unsigned short port, [[maybe_unused]] int32_t nativeErrorCode, [[maybe_unused]] const std::string& why)
{
	LOGE << std::format("msg=Connection to the server failed, SessionType=CommunityServer, IP={}, reason={}", ip, why);

	//어댑터에서 호출 될 경우 콜스택이 다음과 같습니다.
	//NSCommunityServerHandler::ForwardedEventConnectFail <- we are here
	//NSCommunityServerServiceLogicAdapter::ForwardConnectFailEventIntoThis 
	//CNLIOInterfaceAdapter::OnConnectFailed
	//CNLNetEventQueue::Dispatch_Queued_Socket_Connect_Failed
	//CNLNetEventQueue::Dispatch_Net_Events
	//NSClientHandler::Process


	//Connect 시도 실패시 구현 내용을 여기서 구현하세요.
	//임시처리 및 구현 예제로 Reconnect 를 시도합니다.
	//

	//CNLAsioSocket::IOOption option;
	//option.RecvBufferSize = 134217728;
	//option.SendBufferSize = 134217728;
	//option.RecordPacketGenTime = false;

	//if (CNLAsioCore::GetInstance()->Connect(ip, port, this->Get_Service_Logic_Adapter(), option, socketInterface) == false)
	//{
	//	LOGE << "Connect to community Server has failed. cannot try again err : " << CNLGetLastError();
	//}
	//else
	//{
	//	LOGE << "Connect to community Server has failed. trying reconnect. host name -> " << ip << "::" << port;
	//}
}

bool NSCommunityServerHandler::Connect()
{
	if (m_State.state() == EState::Connected)
		return false;

	Service* service = Service::GetInstance();
	m_Wid = service->GetWid();
	std::vector<ConsulServerInfo> infos = service->GetAliveListFromConsul(EServerType::CommunityServer);

	for (const auto& item : infos)
	{
		std::string uniqueKey = item.IP + std::to_string(item.Port);
		if (item.Wid == m_Wid)
		{
			CNLAsioSocket::IOOption optionRemoteCommunityServer;
			optionRemoteCommunityServer.RecvBufferSize = 134217728;
			optionRemoteCommunityServer.SendBufferSize = 134217728;
			optionRemoteCommunityServer.RecordPacketGenTime = false;

			if (NSCommunityServerHandler::GetInstance()->Initialize_Service_Logic_Adpater(200'000'000) == false)
			{
				LOGE << "Failed to allocate net buffer [NSCommunityServerHandler]";
				return false;
			}

			if (CNLAsioCore::GetInstance()->ConnectAndWait<CNLIOInterfaceAdapter>
				(2, item.IP, item.Port, NSCommunityServerHandler::GetInstance()->Get_Service_Logic_Adapter(), optionRemoteCommunityServer) == false)
			{
				LOGE << "Failed to start connect remote community server " << "host name : " << item.IP << "::" << item.Port << " err : " << CNLGetLastError();
				return false;
			}
			return true;
		}
	}

	return false;
}

bool NSCommunityServerHandler::IsConnected()
{
	return m_State.state() == EState::Connected;
}

bool NSCommunityServerHandler::IsTryConnect()
{
	return m_State.state() == EState::TryConnect;
}


bool NSCommunityServerHandler::TryFire(Trigger trigger)
{
	if (!m_State.can_fire(trigger))
		return false;

	m_State.fire(trigger);
	return true;
}
