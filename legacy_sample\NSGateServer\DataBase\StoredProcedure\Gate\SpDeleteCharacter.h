#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpDeleteCharacter : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spDeleteCharacter";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);

public:
	struct Input
	{
		int64_t Cid;
		int32_t DeleteSec;
	} Input;

	SpDeleteCharacter() = default;
	SpDeleteCharacter(const int64_t cid, const int32_t deleteSec);
};
