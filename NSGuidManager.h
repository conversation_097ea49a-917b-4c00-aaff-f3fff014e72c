#pragma once

#include <cstdint>
#include <atomic>
#include <chrono>

// 간단한 GUID 관리자 (싱글톤)
class NSGuidManager
{
public:
    // GUID 타입
    enum class Guid
    {
        General = 0,
        Escrow = 1,
        Character = 2,
        Item = 3,
        Guild = 4,
        // 필요에 따라 추가
    };

    static NSGuidManager* GetInstance()
    {
        static NSGuidManager instance;
        return &instance;
    }

    // GUID 생성
    int64_t GenerateGuid(Guid type = Guid::General)
    {
        // 간단한 구현: 타임스탬프 + 카운터 + 타입
        auto now = std::chrono::system_clock::now();
        auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()).count();
        
        uint64_t counter = m_counter.fetch_add(1);
        
        // 상위 48비트: 타임스탬프
        // 중간 8비트: 타입
        // 하위 8비트: 카운터
        int64_t guid = (timestamp << 16) | (static_cast<int>(type) << 8) | (counter & 0xFF);
        
        return guid;
    }

private:
    NSGuidManager() = default;
    ~NSGuidManager() = default;
    NSGuidManager(const NSGuidManager&) = delete;
    NSGuidManager& operator=(const NSGuidManager&) = delete;

private:
    std::atomic<uint64_t> m_counter{0};
};