# Database_Maria 필수 규칙 및 사용법

## 🔴 핵심 원칙

### 1. CID별 순서 보장 (가장 중요)
- **같은 CID는 항상 같은 스레드에서 처리**
- **같은 CID는 항상 같은 DB 커넥션 사용**
- **CID별 시퀀스(SEQ) 자동 증가로 순서 보장**

### 2. 비동기 처리 후 동일 스레드 복귀
- 게임 로직 스레드에서 DB 요청 → DB 워커 스레드에서 처리 → **원래 게임 로직 스레드로 결과 반환**
- 스레드 안전성 자동 보장

### 3. MariaDB 클라이언트 DLL 전용
- **무조건 MariaDB Connector/C 사용**
- **MySQL 호환성 고려 불필요 (조건부 컴파일 금지)**
- **MariaDB 비동기 API 적극 활용**

## 📋 RecordSet 사용 패턴

### 1️⃣ 단일 행 결과 (레코드가 하나)
```cpp
auto recordSet = queryData->GetRecordSet();
if (recordSet == nullptr)
    return false;

// IsEOF() 호출 없이 바로 데이터 읽기
uint64_t deleteStamptime = 0;
recordSet->GetItem("DeleteReqAt", deleteStamptime);
```

### 2️⃣ 다중 행 결과 (레코드가 여러 개)
```cpp
auto recordSet = queryData->GetRecordSet();
while (!recordSet->IsEOF())  // IsEOF()가 자동으로 다음 행으로 이동
{
    int64_t cid;
    std::string name;
    int32_t level;
    
    recordSet->GetItem("CID", cid);
    recordSet->GetItem("Name", name);
    recordSet->GetItem("Level", level);
    
    // 필요시 continue로 특정 행 스킵
    if (level < 10)
        continue;
        
    // 데이터 처리...
}
```

### 3️⃣ 배치 프로시저 결과 (여러 프로시저, 여러 RecordSet)
```cpp
// NSStoredProcedureBatch 사용 시 각 프로시저별 RecordSet 획득
auto characterRecordSet = queryData->GetRecordSet(SpGetCharacterList::GetName());
auto equipRecordSet = queryData->GetRecordSet(SpGetEquipmentListForLobby::GetName());

if (characterRecordSet == nullptr || equipRecordSet == nullptr)
{
    LOGD << "Failed to get recordSet";
    return;
}

// 각 RecordSet 독립적으로 처리
while (!characterRecordSet->IsEOF())
{
    NPCharacterListInfo info;
    characterRecordSet->GetItem("CID", info.CID);
    characterRecordSet->GetItem("Name", info.Name);
    // ...
}

while (!equipRecordSet->IsEOF())
{
    // 장비 정보 처리...
}
```

## 🎯 호출 방법

### 1. 일반 쿼리/프로시저 호출
```cpp
// 비동기 호출 - Promise 패턴
auto promise = NSDataBaseManager::GetInstance()->StartQuery<SpGetUserInfo>(
    connection,
    serializer,
    transactionId
);

// 결과 처리
promise.Then([](auto queryData) {
    auto recordSet = queryData->GetRecordSet();
    // 결과 처리...
});
```

### 2. Storage 컴포넌트 업데이트
```cpp
// NSStorageUpdater를 통한 컴포넌트 업데이트
void NSStorageUpdater::Commit(
    const TYPE_RESULT_MODEL_FUNC& resultFunc, 
    std::source_location location)
{
    QueryFunc(EStorageUpdateMethod::Normal, resultFunc, location, false, 0);
}
```

## ⚠️ 주의사항

### 1. RecordSet 사용 규칙
- **단일 행**: IsEOF() 호출 없이 바로 GetItem() 사용
- **다중 행**: while (!recordSet->IsEOF()) 패턴 필수
- **IsEOF()는 자동으로 다음 행으로 이동** (별도 MoveNext() 불필요)

### 2. 스레드 라우팅 규칙
- **동일한 CID는 반드시 동일한 DB 워커 스레드에서 처리**
- **동일한 CID는 반드시 동일한 DB 커넥션 사용**
- 이 규칙으로 인해 같은 CID의 모든 DB 작업은 순차적으로 처리됨

### 3. API 호환성
- 기존 게임서버 코드 수정 불필요
- `GetAdoRecordSet()` → `GetRecordSet()` 이름만 변경
- 나머지 인터페이스는 100% 호환

## 🔧 내부 동작

1. **StartQuery 호출** (게임 로직 스레드)
2. **CID 기반 스레드 라우팅** (GetExecutorByShardKey)
3. **해당 DB 워커 스레드에서 프로시저 실행**
4. **Promise로 결과 반환** (원래 게임 로직 스레드)
5. **RecordSet으로 결과 처리**

## 💡 성능 최적화

- PreparedStatement 캐싱 (LRU)
- 스레드별 전용 DB 커넥션
- CID별 순서 보장으로 트랜잭션 최소화
- 데드락 자동 재시도 (RetryPolicy)

---

**이 규칙을 준수하면 게임서버 코드 변경 없이 안정적인 DB 처리가 가능합니다.**