#include "stdafx.h"
#include "NSGateTask.h"

int NSGateTask::sm_seedId = 0;

NSGateTask::NSGateTask()
{
	m_id = ++sm_seedId;
}

NSGateTask::~NSGateTask()
{
}

void NSGateTask::Run()
{
	m_Function();
}

void NSGateTask::Reset()
{
	m_executionTime = 0;
	m_Function = nullptr;
}

int32_t NSGateTask::GetId() const
{
	return m_id;
}

uint64_t NSGateTask::GetRunTime() const
{
	return m_executionTime;
}

void NSGateTask::SetTask(uint64_t executionTime, std::function<void()> function)
{
	m_executionTime = executionTime;
	m_Function = function;
}

bool NSTaskCompare::operator()(const NSGateTask* left, const NSGateTask* right) const
{
	return left->GetRunTime() < right->GetRunTime();
}
