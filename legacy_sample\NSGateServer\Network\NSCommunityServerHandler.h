#pragma once
#include "Pool/NSPool.h"
#include "Network/NSCommunityServerSession.h"
#include "Packet/NSPacketData.h"
#include "NSSingleton.h"
#include "NSModels/NSModels.h"
#include "NSCommunityServerServiceLogicAdapter.h"
#include "global_common/stateless++/state_machine.hpp"

#include "INetworkHandler.h"

class NSCommunityServerHandler : public INetworkHandler, public CNLServiceLogicAdapterRef<NSCommunityServerServiceLogicAdapter>, public TemplateSingleton<NSCommunityServerHandler>
{
public:
	enum EState
	{
		DisConnect,
		TryConnect,
		Connected,
	};

	using Trigger = EState;
	using Fsm = stateless::state_machine<EState, Trigger>;

public:
	NSCommunityServerHandler();
	~NSCommunityServerHandler() override;

public:
	virtual void OnClose(int64_t socketChannelId, const std::string& error);

	virtual void Process() override;

	virtual void ForwardedEventRecvPacket(int64_t socketInterfaceId, const char* buffer, int32_t size, bool allocateForCopy)																				override;
	virtual void ForwardedEventClose(int64_t socketInterfaceId, int32_t nativeErrorCode, const std::string& why)																							override;
	virtual void ForwardedEventConnectSuccess(const std::shared_ptr<CNLIOInterfaceAdapter>& socketInterface, const std::string& ip, unsigned short port)													override;
	virtual void ForwardedEventConnectFail(const std::shared_ptr<CNLIOInterfaceAdapter>& socketInterface, const std::string& ip, unsigned short port, int32_t nativeErrorCode, const std::string& why)		override;

public:
	void Init(); // 초기화
	void ProcessRecv();
	void ProcessConnect();
	void ProcessClose();

public:
	std::shared_ptr<NSCommunityServerSession> GetSession(int64_t sessionId);
	void RemoveSession(int64_t sessionId);

	bool Send(const char* buffer, int32_t size, int64_t channelId);

	bool Connect();
	bool IsConnected();
	bool IsTryConnect();
	bool TryFire(Trigger trigger);

private:
	std::vector<std::shared_ptr<NSCommunityServerSession>> m_SessionList;
	std::atomic<bool> m_bInitChannel;

	std::vector<NSPacketData*> m_cPacketQueue;
	concurrency::concurrent_queue<std::shared_ptr<NSCommunityServerSession>> m_ConnectQueue;
	concurrency::concurrent_queue<int64_t> m_CloseSessionQueue;

	std::function<void(NSPacket* pcPacketBase)> m_cPacketHandler[static_cast<int32_t>(EServerPacketType::End)];

	std::int32_t m_Wid{ 0 };
	Fsm m_State = EState::DisConnect;
};
