#pragma once
#include <Windows.h>

class NSThread
{
public:
	enum class Priority : int
	{
		TIME_CRITICAL = THREAD_PRIORITY_TIME_CRITICAL,
		HIGHEST = THREAD_PRIORITY_HIGHEST,
		ABOVE_NORMAL = THREAD_PRIORITY_ABOVE_NORMAL,
		NORMAL = THREAD_PRIORITY_NORMAL,
		BELOW_NORMAL = THREAD_PRIORITY_BELOW_NORMAL,
		LOWEST = THREAD_PRIORITY_LOWEST,
		IDLE = THREAD_PRIORITY_IDLE,
	};

	enum Mask
	{
		Mask_0 = 0x1,
		Mask_1 = 0x2,
		Mask_2 = 0x4,
		Mask_3 = 0x8,
		Mask_4 = 0x10,
		Mask_5 = 0x20,
		Mask_6 = 0x40,
		Mask_7 = 0x80,
		Mask_8 = 0x100,
		Mask_9 = 0x200,
		Mask_10 = 0x400,
		Mask_11 = 0x800,
		Mask_12 = 0x1000,
		Mask_13 = 0x2000,
		Mask_14 = 0x4000,
		Mask_15 = 0x8000,
		Mask_16 = 0x10000,
	};

public:
	NSThread(const NSThread&) = delete;
	NSThread& operator=(const NSThread&) = delete;
	NSThread(NSThread&& rhs) noexcept;
	NSThread& operator=(NSThread&& rhs) noexcept;

	NSThread() = default;
	explicit NSThread(const std::function<void()>& function);
	~NSThread();

	bool IsRunning() const;
	auto GetExitCode() const->DWORD;

	void Wait();
	bool Wait(DWORD milliseconds);

	auto GetThreadId() const->DWORD;
	auto GetPriority() const->Priority;

	void SetAffinity(Mask mask);
	void SetPriority(Priority priority);
	void SetDescription(const std::string& description);

	//static bool SetDescription(HANDLE handle, const wchar_t* description);

private:
	bool HasValidHandle() const;

private:
	static DWORD NSThread_EntryPoint(LPVOID parameter);

private:
	HANDLE m_Handle = NULL;
	DWORD m_ThreadId = 0;
};