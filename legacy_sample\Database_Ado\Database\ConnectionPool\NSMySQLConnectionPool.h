#pragma once

#include "Database/NSConnectionPool.h"

class NSMySQLConnectionPool : public NSConnectionPool
{
public:
	NSMySQLConnectionPool(const std::string_view host, uint32_t port,
		const std::string_view dbName, const std::string_view user, const std::string_view password);
	virtual ~NSMySQLConnectionPool() = default;

private:
	auto Alloc() -> NSAdoConnection* override;

	// mysql version
	DataTypeEnum GetDataTypeEnum(std::string_view type);
	ParameterDirectionEnum GetParameterDirectionEnum(std::string_view type);

	std::unordered_map<std::string, DataTypeEnum> m_DataTypeMap;
};
