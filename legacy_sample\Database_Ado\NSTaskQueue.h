#pragma once

class NSTaskPriority
{
public:
	NSTaskPriority() = default;
	virtual bool IsAvailableTask() const = 0;

	bool operator>(const NSTaskPriority& other) const
	{
		return m_TaskTimestamp > other.m_TaskTimestamp;
	}

	bool operator>(const uint64_t timestamp) const
	{
		return m_TaskTimestamp > timestamp;
	}

	bool operator<(const NSTaskPriority& other) const
	{
		return m_TaskTimestamp < other.m_TaskTimestamp;
	}

	bool operator<(const uint64_t timestamp) const
	{
		return m_TaskTimestamp < timestamp;
	}

	bool operator==(const NSTaskPriority& other) const
	{
		return m_TaskTimestamp == other.m_TaskTimestamp;
	}

	bool operator==(const uint64_t timestamp) const
	{
		return m_TaskTimestamp == timestamp;
	}

	bool operator>=(const NSTaskPriority& other) const
	{
		return m_TaskTimestamp >= other.m_TaskTimestamp;
	}

	bool operator>=(const uint64_t timestamp) const
	{
		return m_TaskTimestamp >= timestamp;
	}

	bool operator<=(const NSTaskPriority& other) const
	{
		return m_TaskTimestamp <= other.m_TaskTimestamp;
	}

	bool operator<=(const uint64_t timestamp) const
	{
		return m_TaskTimestamp <= timestamp;
	}

	const uint64_t GetTaskTimestamp() const
	{
		return m_TaskTimestamp;
	}

	void SetTaskTimestamp(uint64_t timestamp)
	{
		m_TaskTimestamp = timestamp;
	}

private:
	uint64_t m_TaskTimestamp = 0;
};

template <typename T>
class NSTaskQueue
{
	static_assert(std::is_base_of<NSTaskPriority, T>::value, "T must be derived from NSTaskPriority");

public:
	NSTaskQueue() = delete;
	NSTaskQueue(int32_t throttle, uint64_t interval) : m_Throttle(throttle), m_Interval(interval) {}

	void AdjustThrottle(int32_t throttle)
	{
		m_Throttle = throttle;
	}

	void AdjustInterval(uint64_t interval)
	{
		m_Interval = interval;
	}

	auto GetQueueSize() const -> size_t
	{
		return m_Queue.size();
	}

	void Enqueue(const T& task)
	{
		m_Queue.push(task);
	}

	void SetProcessFunc(std::function<void(const T& data)> func)
	{
		m_ProcessFunc = func;
	}

	void Process(const uint64_t now)
	{
		if (now < m_NextProcess)
			return;

		m_NextProcess = now + m_Interval;

		for (int32_t i = 0; i < m_Throttle; ++i)
		{
			if (m_Queue.empty())
				break;

			const auto& task = m_Queue.top();
			if (task > now)
				break;

			if (task.IsAvailableTask())
			{
				m_ProcessFunc(task);
			}

			m_Queue.pop();
		}
	}

private:
	int32_t m_Throttle = 0;
	uint64_t m_Interval = 0;
	uint64_t m_NextProcess = 0;

	std::function<void(const T& data)> m_ProcessFunc;
	std::priority_queue<T, std::vector<T>, std::greater<T>> m_Queue;
};