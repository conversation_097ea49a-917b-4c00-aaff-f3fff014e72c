#pragma once

#include "System/SystemBase/NSGateSystemBase.h"

class NSQueryData;
class NPPacketCheckCharacterNameReq;
class NPPacketCharacterListReq;
class NPPacketCreateCharacterReq;
class NPPacketDeleteCharacterReq;
class NPPacketDeleteCharacterImmediateReq;
class NPPacketSelectCharacterReq;
class NPPacketGetCharacterEquipmentsReq;
class NPPacketUndeleteCharacterReq;
class NSDataSerializer;
class NSClientSession;
class NSGateCharacterSystem : public NSGateSystemBase
{
public:
	constexpr static auto CHECK_VALID_NAME_EXACT = false;

	explicit NSGateCharacterSystem(NSGateModule* gameModule);
	virtual ~NSGateCharacterSystem() = default;

public:
	virtual bool Init() override;
	virtual bool Reset() override;

	void PacketCheckCharacterNameReq(std::shared_ptr<NSClientSession>& session, NPPacketCheckCharacterNameReq* packet);
	void PacketCharacterListReq(std::shared_ptr<NSClientSession>& session, NPPacketCharacterListReq* packet);
	void PacketCreateCharacterReq(std::shared_ptr<NSClientSession>& session, NPPacketCreateCharacterReq* packet);
	void PacketDeleteCharacterReq(std::shared_ptr<NSClientSession>& session, NPPacketDeleteCharacterReq* packet);
	void PacketDeleteCharacterImmediateReq(std::shared_ptr<NSClientSession>& session, NPPacketDeleteCharacterImmediateReq* packet);
	void PacketUndeleteCharacterReq(std::shared_ptr<NSClientSession>& session, NPPacketUndeleteCharacterReq* packet);
	void PacketSelectCharacterReq(std::shared_ptr<NSClientSession>& session, NPPacketSelectCharacterReq* packet);

private:
	void SetDBQueryForLobbyCharacter(const int64_t aid, const int32_t wid, NSDataSerializer& dataSerializer);

	static void ResultCheckCharacterName(const std::shared_ptr<NSQueryData> queryData);
	static void ResultGetCharacterList(const std::shared_ptr<NSQueryData> queryData);
	static void ResultCreateCharacter(const std::shared_ptr<NSQueryData> queryData);
	static void ResultDeleteCharacter(const std::shared_ptr<NSQueryData> queryData);
	static void ResultDeleteImmediateCharacter(const std::shared_ptr<NSQueryData> queryData);
	static void ResultUndeleteCharacter(const std::shared_ptr<NSQueryData> queryData);
	static void ResultGetCharacterRegionId(const std::shared_ptr<NSQueryData> queryData);
};
