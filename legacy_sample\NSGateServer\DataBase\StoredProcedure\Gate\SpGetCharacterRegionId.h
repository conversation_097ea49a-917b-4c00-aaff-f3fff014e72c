#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpGetCharacterRegionId : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spGetCharacterRegionId";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);
	EErrorCode MakeOutput(NSAdoCommand* command);

public:
	struct Input
	{
		int64_t Aid{ 0 };
		int64_t Cid{ 0 };
	} Input;

	struct Output
	{
		int32_t RegionId{ 0 };
	} Output;

	SpGetCharacterRegionId() = default;
	SpGetCharacterRegionId(const int64_t Aid, const int64_t Cid);
};
