#include "stdafx.h"
#include "NSClientHandler.h"

#include "Packet/NSPacketPool.h"
#include "NPPacket.h"
#include "NSClientSession.h"
#include "GameServer/NSWorldServerManager.h"
#include "DataBase/NSDataBaseManager.h"
#include "Network/NSClientSessionManager.h"
#include "NSGatePacketRouter.h"
#include "Module/NSGateModuleManager.h"
#include "CNLIOInterfaceAdapter.h"
#include "Log/NSActionLog.h"

NSClientHandler::NSClientHandler()
{
}

NSClientHandler::~NSClientHandler()
{
}

void NSClientHandler::OnClose(int64_t socketChannelId, const std::string& error)
{
	if (error.empty())
	{
		LOGD << std::format("OnClose SocketChannelID: {}", socketChannelId);
	}
	else
	{
		LOGE << std::format("OnClose SocketChannelID: {} ({})", socketChannelId, error);
	}

	m_CloseSessionQueue.push(socketChannelId);
}

void NSClientHandler::Process()
{
	//IO 관련 처리는 자동으로 Dispatch_Net_Events에서 자동으로 일어납니다.
	Dispatch_Net_Events();
	ProcessAccept();
	ProcessRecvPacket();
	ProcessGameLogic();

	NSClientSessionManager::GetInstance()->CleanZombie();
}

void NSClientHandler::PostProcess()
{
	ProcessClose();
}

void NSClientHandler::ProcessAccept()
{
	std::shared_ptr<NSClientSession> session = nullptr;
	while (m_AcceptSessionQueue.try_pop(session))
	{
		if (!NSClientSessionManager::GetInstance()->AddSession(session))
		{
			LOGE << std::format("Duplication SessionId [{}]", session->GetSessionId());
			if (session->GetSocketChannel())
			{
				session->GetSocketChannel()->PostCloseTask();
			}
		}

		LOGI << std::format("AddSession  session SessionId[{}]", session->GetSessionId());

		//session->SetWorldServer(NSWorldServerManager::GetInstance()->GetMainWorldServer());

		//Login 모듈에 넣어주자.
		if (!NSGatePacketRouter::GetInstance()->MoveLoginModule(session))
		{
			LOGE << std::format("Fail MoveLoginModule SessionId[{}]", session->GetSessionId());
			session->Close();
		}
	}
}

void NSClientHandler::ProcessClose()
{
	int64_t socketChannelId = 0;
	while (m_CloseSessionQueue.try_pop(socketChannelId))
	{
		std::shared_ptr<NSClientSession> clientSession = NSClientSessionManager::GetInstance()->GetSessionByChannelId(socketChannelId);
		if (clientSession == nullptr)
		{
			LOGE << std::format("clientSession is nullptr SocketChannelId:[{}]", socketChannelId);
			continue;
		}

		NSPacketCloseToWorldReq closeToWorld;
		closeToWorld.SetSessionId(clientSession->GetSessionId());
		if (!clientSession->WorldServerSend(&closeToWorld))
		{
			clientSession = NSClientSessionManager::GetInstance()->RemoveSession(socketChannelId);
			if (clientSession != nullptr)
			{
				NSGatePacketRouter::GetInstance()->CloseSession(socketChannelId);
				if (clientSession->GetSocketChannel())
				{
					if (clientSession->GetWorldMapCell() != nullptr)
					{
						clientSession->GetWorldMapCell()->DelWorldMapPlayer(clientSession->GetSessionId());
					}

					LOGI << std::format("Client Close SessionId:[{}], channelId:[{}] ", clientSession->GetSessionId(), clientSession->GetSocketChannelId());
					clientSession->GetSocketChannel()->PostCloseTask();
				}
			}
		}
		NSActionLog::LogoutWorld(clientSession);
		NSActionLog::DisconnectUser(clientSession);
	}
}

void NSClientHandler::ProcessRecvPacket()
{
	for (NSPacketData* packetData : m_ReceivePacketQueue)
	{
		// 모듈에서 패킷 큐 처리도 지원
		NSGatePacketRouter::GetInstance()->PushPacket(packetData);
	}
	m_ReceivePacketQueue.clear();
}

void NSClientHandler::ProcessGameLogic()
{
	//게임모듈 업데이트
	NSGateModuleManager::GetInstance()->Process();
}

void NSClientHandler::ForwardedEventAccept(const std::shared_ptr<CNLIOInterfaceAdapter>& socketInterface)
{
	//어댑터에서 호출 될 경우 콜스택이 다음과 같습니다.
	//NSClientHandler::ForwardedEventAccept <- we are here
	//NSClientServiceLogicAdapter::ForwardAcceptEventIntoThis 
	//CNLIOInterfaceAdapter::OnAccept
	//CNLNetEventQueue::Dispatch_Queued_Socket_Accepted
	//CNLNetEventQueue::Dispatch_Net_Events
	//NSClientHandler::Process
	//OnAccept 가 하던 역할을 어댑터 함수가 처리합니다.
	if (socketInterface == nullptr)
		return;

	std::shared_ptr<NSClientSession> session(std::make_shared<NSClientSession>());
	if (session == nullptr)
	{
		socketInterface->Close(nullptr);
		return;
	}

	session->SetSocketChannel(socketInterface);

	NSClientSessionManager::GetInstance()->UpdateUnActiveChannelId(session);
	m_AcceptSessionQueue.push(session);
}

void NSClientHandler::ForwardedEventRecvPacket(int64_t socketInterfaceId, const char* buffer, int32_t size, bool allocateForCopy)
{
	//어댑터에서 호출 될 경우 콜스택이 다음과 같습니다.
	//NSClientHandler::ForwardedEventRecvPacket <- we are here
	//NSClientServiceLogicAdapter::ForwardRecvPacketEventIntoThis 
	//CNLIOInterfaceAdapter::OnRecvPacket
	//CNLNetEventQueue::Dispatch_Queued_Packet_Arrived
	//CNLNetEventQueue::Dispatch_Net_Events
	//NSClientHandler::Process
	//OnReceive 가 하던 역할을 어댑터 함수가 처리합니다.

	if (buffer == nullptr)
	{
		LOGE << "PacketBuffer IS NULL";
		return;
	}

	if (size < 0)
	{
		LOGE << "PacketSize is invalid. negative value.";
		return;
	}

	auto packet = reinterpret_cast<const NPPacket*>(buffer);
	if (packet->GetSize() != static_cast<uint32_t>(size))
	{
		LOGE << "PacketSize is invalid.";
		return;
	}


	NSPacketData* packetData = nullptr;
	if (allocateForCopy)
	{
		packetData = NSPacketPool::GetInstance()->ReceiveAlloc(size);
		if (packetData == nullptr)
		{
			LOGE << "PacketData IS NULL";
			return;
		}

		if (packetData->GetSize() < size)
		{
			NSPacketPool::GetInstance()->ReceiveDeAlloc(packetData);
			LOGE << "PacketData Size Over";
			return;
		}

		memcpy(packetData->GetBuffer(), buffer, size);
	}
	else
	{
		//param buffer 가 NetEventBuffer 의 영역인 경우
		//해당 영역은 서비스로직에서 다음 틱이 시작될 때까지 안전합니다.
		//NSPacketData 객체만 할당하고, 스트림을 복사하지 않아도 됩니다.
		packetData = new NSPacketData(buffer, size, true, true);
		if (packetData == nullptr)
		{
			LOGE << "Allocate for NSPacketData(" << sizeof(NSPacketData) << ") byte failed";
			return;
		}

	}

	packetData->SetSocketChannelID(static_cast<int64_t>(socketInterfaceId));
	m_ReceivePacketQueue.push_back(packetData);
}

void NSClientHandler::ForwardedEventClose(int64_t socketInterfaceId, [[maybe_unused]] int32_t nativeErrorCode, const std::string& why)
{
	//어댑터에서 호출 될 경우 콜스택이 다음과 같습니다.
	//NSClientHandler::ForwardedEventClose <- we are here
	//NSClientServiceLogicAdapter::ForwardCloseEventIntoThis <- we are here
	//CNLIOInterfaceAdapter::OnDisconnected
	//CNLNetEventQueue::Dispatch_Queued_Socket_Disconnected
	//CNLNetEventQueue::Dispatch_Net_Events
	//NSClientHandler::Process
	//OnClose는 그대로 사용되기 때문에, 인자를 맞춰서 전달합니다.

	OnClose(socketInterfaceId, why);
}