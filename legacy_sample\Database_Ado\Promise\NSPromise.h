#pragma once
#include <span>
#include "Detail/Traits.h"
#include "Detail/Utility.h"

#include "NSCompletionToken.h"
#include "NSExecutor.h"

class NSPromiseBase {};

template <typename T>
class NSPromise : public NSPromiseBase
{
public:
	NSPromise(const NSPromise&) = delete;
	NSPromise& operator=(const NSPromise&) = delete;

	NSPromise() = default;
	NSPromise(NSPromise&&) noexcept = default;
	NSPromise& operator=(NSPromise&&) noexcept = default;

	explicit NSPromise(NSCompletionToken<T> completionToken);

	bool IsValid() const;
	bool IsDone() const;

	void Wait();
	void WaitFor(const std::chrono::milliseconds& milliseconds);

	auto Get() -> decltype(auto);

	template <typename Callable, typename Traits = Promise::Detail::Traits<Callable>>
	auto Then(Callable&& callable, Promise::NSExecutor& executor = Promise::CurrentExecutor());

	template <typename Callable, typename Traits = Promise::Detail::Traits<Callable>>
	auto ContinuationWith(Callable&& callable, Promise::NSExecutor& executor = Promise::CurrentExecutor());

private:
	NSCompletionToken<T> m_CompletionToken = NSCompletionToken<T>(NullConstructToken{});
};

template <typename T>
NSPromise<T>::NSPromise(NSCompletionToken<T> completionToken)
	: m_CompletionToken(std::move(completionToken))
{
	assert(m_CompletionToken.IsValid());
}

template <typename T>
bool NSPromise<T>::IsValid() const
{
	return m_CompletionToken.IsValid();
}

template <typename T>
bool NSPromise<T>::IsDone() const
{
	return m_CompletionToken.IsDone();
}

template <typename T>
void NSPromise<T>::Wait()
{
	m_CompletionToken.Wait();
}

template <typename T>
void NSPromise<T>::WaitFor(const std::chrono::milliseconds& milliseconds)
{
	m_CompletionToken.WaitFor(milliseconds);
}

template <typename T>
auto NSPromise<T>::Get() -> decltype(auto)
{
	return m_CompletionToken.Get();
}

template <typename T>
template <typename Callable, typename Traits>
auto NSPromise<T>::Then(Callable&& callable, Promise::NSExecutor& executor)
{
	using result_type = typename Traits::return_type;
	constexpr size_t arity = Traits::Arity;

	NSCompletionToken<result_type> continuationCompletionToken;

	m_CompletionToken.AddCompletionCallback(std::function<void()>([executor = &executor, ct = m_CompletionToken, ctt = continuationCompletionToken, fn = WRAP_MOVE_ONLY_CALLABLE(callable)]() mutable
	{
		executor->Post([executor, ct = std::move(ct), ctt = std::move(ctt), fn = std::move(fn)]() mutable
		{
			// continuation callable use param
			if constexpr (arity == 1)
			{
				static_assert(!std::is_same_v<T, void>, "NSPromise<void> return 'void'. Then() cannot use param");

				if constexpr (std::is_same_v<result_type, void>)
				{
					fn(ct.Get());
					ctt.Set();
				}
				else
				{
					ctt.Set(fn(ct.Get()));
				}
			}
			else
			{
				if constexpr (std::is_same_v<result_type, void>)
				{
					fn();
					ctt.Set();
				}
				else
				{
					ctt.Set(fn());
				}
			}
		});
	}));

	return NSPromise<result_type>(std::move(continuationCompletionToken));
}

template <typename T>
template <typename Callable, typename Traits>
auto NSPromise<T>::ContinuationWith(Callable&& callable, Promise::NSExecutor& executor)
{
	using result_type = typename Traits::return_type;
	constexpr size_t arity = Traits::Arity;

	NSCompletionToken<result_type> cct;

	m_CompletionToken.AddCompletionCallback(std::function<void()>([executor = &executor, ct = m_CompletionToken, cct = cct, fn = WRAP_MOVE_ONLY_CALLABLE(callable)]() mutable
	{
		executor->Post([executor, ct = std::move(ct), ctt = std::move(cct), fn = std::move(fn)]() mutable
		{
			// this-NSPromise<T>- is used as a param
			if constexpr (arity == 1 && std::is_invocable_v<Callable, NSPromise<T>&>)
			{
				NSPromise<T> temp(ct);

				if constexpr (std::is_same_v<result_type, void>)
				{
					fn(temp);
					ctt.Set();
				}
				else
				{
					ctt.Set(fn(temp));
				}
			}
			else
			{
				static_assert(!sizeof(T));
			}
		});
	}));

	return NSPromise<result_type>(std::move(cct));
}

namespace Promise
{
	template <typename... Ts>
	auto WaitAll(Ts&... promises)
		-> std::enable_if_t<std::conjunction_v<std::is_base_of<NSPromiseBase, std::remove_cvref_t<Ts>>...>, NSPromise<void>>
	{
	    NSCompletionToken<void> completionToken;
	    auto results = std::make_shared<std::atomic<size_t>>(0);

	    auto addContinuation = [completionToken, results] <typename T> (NSPromise<T>& p)
	    {
	        p.Then([results, completionToken]() mutable
	            {
	                constexpr size_t count = sizeof...(Ts);
	                size_t prev = results->fetch_add(1);

	                if (prev + 1== count)
	                {
	                    completionToken.Set();
	                }
	            });
	    };
	    (..., addContinuation(promises));

		return NSPromise<void>(std::move(completionToken));
	}

	template <typename R>
		requires std::ranges::forward_range<R> && std::derived_from<std::ranges::range_value_t<R>, NSPromiseBase>
	auto WaitAll(R&& range) -> NSPromise<void>
	{
		NSCompletionToken<void> completionToken;
		auto results = std::make_shared<std::atomic<size_t>>(0);
		size_t count = std::ranges::distance(range);

		for (decltype(auto) p : range)
		{
			p.Then([results, count, completionToken]() mutable
				{
					size_t prev = results->fetch_add(1);

					if (prev + 1 == count)
					{
						completionToken.Set();
					}
				});
		}

		return NSPromise<void>(std::move(completionToken));
	}

	template <typename... Ts>
	auto WaitAny(Ts&... promises)
		-> std::enable_if_t<std::conjunction_v<std::is_base_of<NSPromiseBase, std::remove_cvref_t<Ts>>...>, NSPromise<void>>
	{
		NSCompletionToken<void> completionToken;
		auto results = std::make_shared<std::atomic<bool>>(false);

		auto addContinuation = [completionToken, results] <typename T> (NSPromise<T>& p)
		{
			p.Then([results, completionToken]() mutable
				{
					bool expected = false;
					if (results->compare_exchange_strong(expected, true))
					{
						completionToken.Set();
					}
				});
		};
		(..., addContinuation(promises));

		return NSPromise<void>(std::move(completionToken));
	}

	template <typename Callable, typename Traits = Detail::Traits<Callable>, typename R = typename Traits::return_type>
	auto Post(NSExecutor& executor, Callable&& callable) -> NSPromise<R>
	{
		NSCompletionToken<R> ct;

		executor.Post([ct = ct, fn = WRAP_MOVE_ONLY_CALLABLE(callable)]() mutable
		{
			if constexpr (std::is_same_v<void, R>)
			{
				fn();
				ct.Set();
			}
			else
			{
				ct.Set(fn());
			}
		});

		return NSPromise<R>(std::move(ct));
	}

	auto Delay(uint64_t milliseconds) -> NSPromise<void>;
	auto Schedule(const std::function<void()>& function, uint64_t milliseconds) -> std::stop_source;
	auto Schedule(NSExecutor& executor, const std::function<void()>& function, uint64_t milliseconds) -> std::stop_source;
}