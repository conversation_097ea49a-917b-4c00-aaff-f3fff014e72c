#pragma once
#include <unordered_set>

#ifndef _WIN32_WINNT
#define _WIN32_WINNT 0x0A00
#endif

#pragma warning(push)
#pragma warning(disable:6001 6031 6255 6258 6262 6387 6397 6495 26437 26439 26495 26819)
#include <asio/asio.hpp>
#pragma warning(pop)

#include "Promise/NSExecutor.h"
#include "Win32/NSThread.h"

namespace Promise::Detail
{
	class ThreadPool : public NSExecutor
	{
	private:
		ThreadPool();
		~ThreadPool();

	public:
		bool RunningInThisThread() const override;

		void Post(const std::function<void()>& function) override;
		void Delay(const std::function<void()>& function, uint64_t milliseconds);

		static auto GetInstance() -> ThreadPool&;

	private:
		asio::io_context m_IoContext;
		asio::executor_work_guard<asio::io_context::executor_type> m_WorkGuard;
		std::vector<NSThread> m_Workers;
		std::unordered_set<std::thread::id> m_WorkerIds;
	};
}
