#pragma once

#include <CNLCoreIO.h>

class NPPacket;
class NSPacket;
class NSPacketData;
class NSWorldServer;
class CNLIOInterfaceAdapter;

class NSWorldServerSession : public std::enable_shared_from_this<NSWorldServerSession>
{
public:
	NSWorldServerSession();
	virtual ~NSWorldServerSession();

public:
	void Reset();
	auto GetSocketChannelId() const ->int64_t;
	void SetChannel(const std::shared_ptr<CNLIOInterfaceAdapter>& channel);
	auto GetChannel() const -> const std::shared_ptr<CNLIOInterfaceAdapter>&;
	auto GetIP() const -> const std::string&;

	template<typename TPacket>
	void Send(TPacket& packet)
	{
		assert(m_SocketChannel != nullptr);
		m_SocketChannel->Send(reinterpret_cast<const char*>(&packet), (cnl_packet_size_t)packet.GetSize());
	}

	void Send(const char* packetData, uint32_t dataSize)
	{
		assert(m_SocketChannel != nullptr);
		m_SocketChannel->Send(packetData, dataSize);
	}

	void Close() const;

public:
	void SetWorldServer(std::shared_ptr<NSWorldServer> server);
	auto GetWorldServer() const -> std::shared_ptr<NSWorldServer>;

private:
	std::shared_ptr<NSWorldServer> m_WorldServer;
	std::shared_ptr<CNLIOInterfaceAdapter> m_SocketChannel = nullptr;
};
