#include "stdafx.h"
#include "NSGateModule.h"

#include "NPModels.h"
#include "Entity/NSScheduledTaskManager.h"
#include "Packet/NSPacketData.h"
#include "Packet/NSPacketPool.h"
#include "Network/NSClientSession.h"
#include "System/SystemBase/NSGateSystemBase.h"
#include "Network/NSClientSessionManager.h"
#include "System/NSGateGlobalSystem.h"

NSGateModule::NSGateModule() :
	m_ModuleId(0),
	m_ScheduledTaskManager(std::make_unique<NSScheduledTaskManager>())
{
	AddSystem<NSGateGlobalSystem>(this);
}

NSGateModule::~NSGateModule()
{
	m_ProcessorSystems.clear();
}

void NSGateModule::Init()
{
	m_PacketProcessor.clear();

	//기본으로 등록해야할 패킷핸들러가 있다면 등록해야한다.

	for (const auto& [_, system] : m_ProcessorSystems)
	{
		if (system != nullptr)
		{
			system->Init();
		}
	}
}

void NSGateModule::Reset()
{
	for (const auto& [_, system] : m_ProcessorSystems)
	{
		if (system != nullptr)
		{
			system->Reset();
		}
	}
}

void NSGateModule::Process(const uint64_t nowTick, [[maybe_unused]] uint64_t elapsedTickCount)
{
	m_ScheduledTaskManager->RunScheduledTask(nowTick, false);

	while (!m_QueuePacketData.empty())
	{
		const auto packetData = m_QueuePacketData.front();
		HandlePacket(packetData);
		m_QueuePacketData.pop();

		NSPacketPool::GetInstance()->ReceiveDeAlloc(packetData);
	}

	for (const auto& [_, system] : m_ProcessorSystems)
	{
		if (!system)
			continue;

		system->Process();
	}
}

int32_t NSGateModule::GetModuleID() const
{
	return m_ModuleId;
}

void NSGateModule::SetModuleID(const int32_t moduleId)
{
	m_ModuleId = moduleId;
}

void NSGateModule::InsertScheduledTask(const uint64_t delay, const std::function<void()>& function) const
{
	m_ScheduledTaskManager->InsertScheduledTask(delay, function);
}

void NSGateModule::InsertScheduledTaskWithoutInstantRun(const uint64_t delay, const std::function<void()>& function) const
{
	m_ScheduledTaskManager->InsertScheduledTaskWithoutInstantRun(delay, function);
}

void NSGateModule::ClearScheduledTask() const
{
	m_ScheduledTaskManager->ClearScheduledTask();
}

bool NSGateModule::AddSession(std::shared_ptr<NSClientSession> session)
{
	if (session == nullptr)
	{
		LOGE << std::format("[Server] NSGateModule::AddSession failed - Session nullptr");
		return false;
	}

	if (!m_SessionByChannelId.emplace(session->GetSocketChannelId(), session).second)
	{
		LOGE << std::format("[Server] NSGateModule::AddSession failed - map insert failed[{}] ModuleID[{}]",
			session->GetSocketChannelId(), m_ModuleId);
		return false;
	}

	return OnAddSession(session);
}

bool NSGateModule::MoveSession(std::shared_ptr<NSClientSession> session)
{
	if (session == nullptr)
	{
		LOGE << std::format("[Server] NSGateModule::MoveSession failed - Session nullptr");
		return false;
	}

	if (!m_SessionByChannelId.emplace(session->GetSocketChannelId(), session).second)
	{
		LOGE << std::format("[Server] NSGateModule::MoveSession failed - map insert failed[{}] ModuleID[{}]",
			session->GetSocketChannelId(), m_ModuleId);
		return false;
	}

	session->SetModuleID(GetModuleID());

	return OnMoveSession(session);
}

bool NSGateModule::RemoveSession(std::shared_ptr<NSClientSession> session)
{
	if (session->GetSocketChannelId() < 0)
	{
		LOGE << std::format("[Server] NSGateModule::RemoveSession failed - Session nullptr");
		return false;
	}

	const auto it = m_SessionByChannelId.find(session->GetSocketChannelId());
	if (it == m_SessionByChannelId.end())
	{
		LOGE << std::format("[Server] NSGateModule::RemoveSession failed - no session[{}]", session->GetSocketChannelId());
		return false;
	}

	m_SessionByChannelId.erase(it);

	return OnRemoveSession(session);
}

bool NSGateModule::Disconnect(const int64_t channelId)
{
	const auto itr = m_SessionByChannelId.find(channelId);
	if (itr == m_SessionByChannelId.end())
		return false;

	auto session = itr->second;

	RemoveSession(session);
	return OnDisconnect(session);
}

bool NSGateModule::OnDisconnect(std::shared_ptr<NSClientSession>)
{
	return true;
}

void NSGateModule::PushPacket(NSPacketData* packetData)
{
	m_QueuePacketData.push(packetData);
}

void NSGateModule::HandlePacket(NSPacketData* packetData)
{
	const int64_t socketChannelId = packetData->GetSocketChannelID();
	NPPacket* packet = reinterpret_cast<NPPacket*>(packetData->GetBuffer());
	const uint16_t packetType = packet->GetType();

	if (packet == nullptr)
	{
		LOGW << std::format("HandlePacket handles null packet!");
		return;
	}
	if (packetType <= EPacketType_1000::ePacketType_1000_Begin || EPacketType_1000::ePacketType_1000_End <= packetType)
	{
		LOGW << std::format("HandlePacket handles invalid packet type [Type:{}]", packetType);
		return;
	}

	auto session = GetSessionByChannelId(socketChannelId);
	if (session == nullptr)
		return;

	if (session->IsClosing())
		return;

	const auto it = m_PacketProcessor.find(packetType);
	if (it == m_PacketProcessor.end())
		return;

	const PROCESSHANDLE& processor = it->second;
	processor(session, reinterpret_cast<char*>(packet));
}

std::shared_ptr<NSClientSession> NSGateModule::GetSessionByChannelId(int64_t channelId)
{
	const auto it = m_SessionByChannelId.find(channelId);
	if (it == m_SessionByChannelId.end())
		return nullptr;

	return it->second;
}
