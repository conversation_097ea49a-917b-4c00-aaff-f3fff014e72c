#pragma once

#include "Entity/NSGateTask.h"

class NSGateTask;

class NSScheduledTaskManager
{
public:
	NSScheduledTaskManager();
	virtual ~NSScheduledTaskManager();

public:
	void InsertScheduledTask(uint64_t delay, std::function<void()> function);
	void InsertScheduledTaskWithoutInstantRun(uint64_t delay, std::function<void()> function);
	void RunScheduledTask(uint64_t nowTick, bool forceRun);
	void ClearScheduledTask();

private:
	void InsertScheduledTask_Core(uint64_t delay, bool allowInstantRun, std::function<void()> function);

private:
	std::multiset<NSGateTask*, NSTaskCompare> m_scheduledTaskList;
};
