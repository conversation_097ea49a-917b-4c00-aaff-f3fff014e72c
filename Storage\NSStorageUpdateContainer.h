#pragma once
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include "../NSDefine.h"

// Forward declarations
class NSQueryData;
struct NSStorageUpdateContainer;
class NSDBSession;

// 레거시 타입 정의
using TYPE_RESULT_MODEL_FUNC = std::function<void(const std::shared_ptr<NSQueryData>, NSStorageUpdateContainer*)>;
#define RESULT_MODEL_FUNC(TARGET, RESULTFUNC) std::bind(RESULTFUNC, TARGET, std::placeholders::_1, std::placeholders::_2)

enum class EStorageUpdateMethod
{
    Normal,
    CustomProcedure,
    EscrowDeposit,
    EscrowWithdraw,
    EscrowReclaim
};

// 레거시와 동일한 구조체
struct NSStorageUpdateContainer
{
    std::string ProcedureName;
    int32_t Wid{ 0 };
    int64_t Aid{ 0 };
    int64_t Cid{ 0 };
    std::shared_ptr<NSDBSession> Session{ nullptr };
    int64_t Seq{ 0 };
    int32_t ModelId{ 0 };
    std::vector<std::string> Payloads;
    std::vector<TYPE_RESULT_MODEL_FUNC> Callbacks;
    std::string EscrowPayload;
    int64_t EscrowTransactionId{ 0 };
    EStorageUpdateMethod Method{ 0 };
};