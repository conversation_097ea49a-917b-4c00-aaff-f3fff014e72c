#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpDeleteCharacterImmediate : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spDeleteCharacterImmediate";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);

public:
	struct Input
	{
		int64_t Cid;
		int32_t DeletableLevel;
	} Input;

	SpDeleteCharacterImmediate() = default;
	SpDeleteCharacterImmediate(const int64_t cid, const int32_t deletableLevel);
};