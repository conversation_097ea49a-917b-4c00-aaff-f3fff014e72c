#include "stdafx.h"

#include "NSStorageUpdater.h"
#include "System/Service.h"
#include "Guid/NSGuidManager.h"

NSStorageUpdater::NSStorageUpdater(std::shared_ptr<NSDBSession> session, int64_t aid, int64_t cid, std::source_location location)
	: m_Wid(Service::GetInstance()->GetWid()),
	m_Session(session),
	m_Aid(aid),
	m_Cid(cid)
{
	LOGD << std::format("declared from {}:{}", location.file_name(), location.line());
}

NSStorageUpdater::~NSStorageUpdater()
{
#ifdef _DEBUG
	assert(m_CommitCalled);
#endif
}

void NSStorageUpdater::Disconnect([[maybe_unused]] std::shared_ptr<NSDBSession> session)
{
#ifdef _DEBUG
	DisconnectSession(session, EErrorCode::DBError);
#endif
}

void NSStorageUpdater::MakeEscrowPayload(rapidjson::Writer<rapidjson::StringBuffer>& writer, const NSEscrowData& escrowData)
{
	writer.StartObject();
	{
		writer.Key("SID");
		writer.Int64(escrowData.SenderCid);

		writer.Key("RID");
		writer.Int64(escrowData.ReceiverCid);

		writer.Key("T");
		writer.Int(NPClassCast(escrowData.EscrowType));

		writer.Key("I");
		writer.Int(escrowData.EscrowId);

		writer.Key("Q");
		writer.Int(escrowData.EscrowQuantity);

		writer.Key("UID");
		writer.Int64(escrowData.EscrowUid);
	}
	writer.EndObject();
}

std::vector<std::tuple<int64_t, NSStorageModel*>> NSStorageUpdater::GetModel()
{
	return m_Models;
}

std::vector<TYPE_RESULT_MODEL_FUNC> NSStorageUpdater::GetResultFunc()
{
	return m_ResultFuncs;
}

void NSStorageUpdater::AddModelToUpdate(NSStorageModel* componentModel, std::source_location location)
{
	LOGD << std::format("called from {}:{}", location.file_name(), location.line());

	if (componentModel == nullptr)
	{
		LOGE << std::format("NSStorageUpdater::AddModelToUpdate() has called with empty component.");
		return;
	}

	int32_t typeId = componentModel->GetModelId();
	if (m_ModelIds.insert(typeId).second)
	{
		int64_t newSeq = NSStorageManager::GetInstance()->GetNextSequence(m_Cid);
		if (newSeq == -1)
		{
			LOGE << std::format("NSStorageUpdater::AddModelToUpdate() failed to get a new sequence.");
			DisconnectSession(m_Session, EErrorCode::FailedToGetNewSequence);
			return;
		}
		m_Models.push_back(std::make_tuple(newSeq, componentModel));

		LOGD << std::format("Model {}({}) seq {} ", componentModel->GetModelName(), typeId, newSeq);
	}
	else
	{
		LOGD << std::format("Model {}({}) (duplicated)", componentModel->GetModelName(), typeId);
	}
}

void NSStorageUpdater::AddResultFunc(const TYPE_RESULT_MODEL_FUNC& resultFunc)
{
	m_ResultFuncs.push_back(resultFunc);
}

void NSStorageUpdater::Rollback(std::source_location location)
{
	LOGD << std::format("called from {}:{}", location.file_name(), location.line());

#ifdef _DEBUG
	assert(!m_CommitCalled);
	m_CommitCalled = true;
#endif

	if (!NSStorageManager::GetInstance()->IsRollbackPossible(m_Cid, m_Models.size()))
	{
		LOGE << std::format("rollback is not possible.");
		DisconnectSession(m_Session, EErrorCode::DBError);
		return;
	}

	for (auto& [seq, model] : m_Models)
	{
		model->Rollback();
	}

	if (!NSStorageManager::GetInstance()->RollbackPendingSequence(m_Cid, m_Models.size()))
	{
		LOGE << std::format("rollback failed.");
		DisconnectSession(m_Session, EErrorCode::DBError);
	}
}

void NSStorageUpdater::Commit(const TYPE_RESULT_MODEL_FUNC& resultFunc, std::source_location location)
{
	QueryFunc(EStorageUpdateMethod::Normal, resultFunc, location, false, 0);
}

void NSStorageUpdater::CommitForce(const TYPE_RESULT_MODEL_FUNC& resultFunc, std::source_location location)
{
	QueryFunc(EStorageUpdateMethod::Normal, resultFunc, location, true, 0);
}

void NSStorageUpdater::CommitWithEscrowDeposit(const int64_t escrowReceiverCid, const TYPE_RESULT_MODEL_FUNC& resultFunc, std::source_location location)
{
	m_EscrowReceiverCid = escrowReceiverCid;
	QueryFunc(EStorageUpdateMethod::EscrowDeposit, resultFunc, location, false, 0);
}

void NSStorageUpdater::CommitWithEscrowWithdraw(int64_t escrowTransactionId, const TYPE_RESULT_MODEL_FUNC& resultFunc, std::source_location location)
{
	QueryFunc(EStorageUpdateMethod::EscrowWithdraw, resultFunc, location, false, escrowTransactionId);
}

void NSStorageUpdater::CommitWithEscrowReclaim(const int64_t escrowTransactionId, const TYPE_RESULT_MODEL_FUNC& resultFunc, std::source_location location)
{
	QueryFunc(EStorageUpdateMethod::EscrowReclaim, resultFunc, location, false, escrowTransactionId);
}

void NSStorageUpdater::QueryFunc(EStorageUpdateMethod method, const TYPE_RESULT_MODEL_FUNC& resultFunc, std::source_location location, bool isForce, int64_t escrowTransactionId)
{
	LOGD << std::format("called from {}:{}", location.file_name(), location.line());

#ifdef _DEBUG
	assert(!m_CommitCalled);
	m_CommitCalled = true;

	int64_t debugPendingSeq = NSStorageManager::GetInstance()->GetPendingSequence(m_Cid);
	if (debugPendingSeq != 0)
	{
		size_t debugEstimateCount = debugPendingSeq - NSStorageManager::GetInstance()->GetUpdatedSequence(m_Cid);
		if (m_Models.size() != debugEstimateCount)
		{
			LOGW << std::format("NSStorageUpdater::Commit() estimateCount mismatch. PendingCount: {}, ModelCount: {}", debugEstimateCount, m_Models.size());
		}
	}
#endif

	if (method != EStorageUpdateMethod::Normal &&
		method != EStorageUpdateMethod::CustomProcedure &&
		m_Models.size() != 1)
	{
		LOGE << std::format("NSStorageUpdater::Commit() only allows 1 model with escrow. count: {}", m_Models.size());
		return;
	}

	if (resultFunc != nullptr)
	{
		AddResultFunc(resultFunc);
	}

	int64_t newEscrowTransactionId = method == EStorageUpdateMethod::EscrowDeposit
		? NSGuidManager::GetInstance()->GenerateGuid(NSGuidManager::Guid::Escrow)
		: 0;

	size_t currentIdx = 0;
	size_t modelCount = m_Models.size();
	for (auto& [seq, model] : m_Models)
	{
		++currentIdx;
		if (model == nullptr)
		{
			LOGW << std::format("NSStorageUpdater::Commit() found a nullptr model");
			continue;
		}

		auto container = std::make_shared<NSStorageUpdateContainer>();
		container->Wid = m_Wid;
		container->Aid = m_Aid;
		container->Cid = m_Cid;
		container->Session = m_Session;
		container->Seq = seq;
		container->ModelId = model->GetModelId();
		container->Method = method;

		switch (method)
		{
		case EStorageUpdateMethod::Normal:
		{
			container->ProcedureName = model->GetUpsertName();
		}
		break;
		case EStorageUpdateMethod::EscrowDeposit:
		{
			NSStorageEscrowModel* escrowModel = static_cast<NSStorageEscrowModel*>(model);
			if (escrowModel == nullptr)
			{
				LOGE << std::format("Model {} is not an escrow model.", model->GetModelName());
				return;
			}
			container->ProcedureName = escrowModel->GetEscrowDepositName();
			container->EscrowTransactionId = newEscrowTransactionId;
			escrowModel->SerializeEscrow(container->EscrowPayload, m_EscrowReceiverCid);
		}
		break;
		case EStorageUpdateMethod::EscrowWithdraw:
		{
			NSStorageEscrowModel* escrowModel = static_cast<NSStorageEscrowModel*>(model);
			if (escrowModel == nullptr)
			{
				LOGE << std::format("Model {} is not an escrow model.", model->GetModelName());
				return;
			}
			container->ProcedureName = escrowModel->GetEscrowWithdrawName();
			container->EscrowTransactionId = escrowTransactionId;
		}
		break;

		case EStorageUpdateMethod::EscrowReclaim:
		{
			NSStorageEscrowModel* escrowModel = dynamic_cast<NSStorageEscrowModel*>(model);
			if (escrowModel == nullptr)
			{
				LOGE << std::format("Model {} is not an escrow model.", model->GetModelName());
				return;
			}
			container->ProcedureName = escrowModel->GetEscrowReclaimName();
			container->EscrowTransactionId = escrowTransactionId;
		}
		break;

		}

		if (container->ProcedureName.empty())
		{
			LOGE << std::format("Model {} has invalid UpsertName.", model->GetModelName());
			continue;
		}

		if (method == EStorageUpdateMethod::Normal && isForce)
		{
			model->Serialize(container->Payloads);
		}
		else
		{
			model->SerializeAffected(container->Payloads);
		}

		if (container->Payloads.empty())
		{
			LOGD << std::format("Model {} has empty payload. {}:{}", model->GetModelName(), location.file_name(), location.line());
			continue;
		}

		if (currentIdx == modelCount)
		{
			container->Callbacks.swap(m_ResultFuncs);
		}

		PostQuery(container);
	}
}

bool NSStorageUpdater::PostQuery(const std::shared_ptr<NSStorageUpdateContainer> container)
{
	if (!NSStorageManager::GetInstance()->IsUpdatePossible(container->Cid))
		return PostQueryDelay(1000, container);

	if (container->ProcedureName.empty())
	{
		LOGE << std::format("Model #{} has invalid UpsertName.", container->ModelId);
		return false;
	}

	NSDataBaseManager::GetInstance()->StorageUpdateQuery(container,
		std::bind(&NSStorageUpdater::UpdateFunc, std::placeholders::_1, std::placeholders::_2),
		std::bind(&NSStorageUpdater::ResultFunc, std::placeholders::_1, std::placeholders::_2),
		container->Session);

	return true;
}

bool NSStorageUpdater::PostQueryDelay(int delayMS, const std::shared_ptr<NSStorageUpdateContainer> container)
{
#ifdef _DEBUG
	LOGD << std::format("NSStorageUpdater::PostQueryDelay() has started. delayMS: {}", delayMS);
#endif

	Post(Promise::ThreadPool(),
		[=]()
		{
			std::this_thread::sleep_for(std::chrono::milliseconds(delayMS));
			PostQuery(container);
		});
	return true;
}

EErrorCode NSStorageUpdater::UpdateFunc(const std::shared_ptr<NSQueryData> queryData, const std::shared_ptr<NSStorageUpdateContainer> container)
{
	const auto procedureHost = EDataBase::Game;
	auto connection = NSDataBaseManager::GetInstance()->GetDBConnection(procedureHost);
	if (connection == nullptr)
	{
		LOGE << std::format("failed to connect database. Model #{} seq {} procedure {}",
			container->ModelId, container->Seq, container->ProcedureName);
		return EErrorCode::DBConnectionError;
	}

	NSAdoCommand* command = nullptr;
	command = connection->GetCommand(container->ProcedureName.c_str());
	if (command == nullptr)
	{
		LOGE << std::format("procedure not found from database. procedure {}", container->ProcedureName);
		return EErrorCode::AdoCommandNullptr;
	}

	command->SetItem("WID", container->Wid);
	command->SetItem("AID", container->Aid);
	command->SetItem("CID", container->Cid);
	command->SetItem("SEQ", container->Seq);

	if (container->Method == EStorageUpdateMethod::EscrowDeposit)
	{
		command->SetItem("EscrowTransactionId", container->EscrowTransactionId);
		if (!command->SetItem("EscrowPayload", container->EscrowPayload.c_str()))
			return EErrorCode::DBArgumentError;
	}
	else if (container->Method == EStorageUpdateMethod::EscrowWithdraw)
	{
		command->SetItem("EscrowTransactionId", container->EscrowTransactionId);
	}
	else if (container->Method == EStorageUpdateMethod::EscrowReclaim)
	{
		command->SetItem("EscrowTransactionId", container->EscrowTransactionId);
	}


	size_t payloadCount = container->Payloads.size();
	LOGD << std::format("Cid {} Model #{} Seq {} has {} payloads.", container->Cid, container->ModelId, container->Seq, payloadCount);
	for (size_t i = 0; i < payloadCount; ++i)
	{
		if (!container->Payloads.at(i).empty() && container->Payloads.at(i) != "[]")
		{
			LOGD << std::format("Cid {} Model #{} Seq {} @Payload{}: {}", container->Cid, container->ModelId, container->Seq, i, container->Payloads.at(i).c_str());
		}
		else
		{
			LOGD << std::format("Cid {} Model #{} Seq {} @Payload{} is empty", container->Cid, container->ModelId, container->Seq, i);
		}

		if (!command->SetItem(std::format("Payload{}", i).c_str(), container->Payloads.at(i).c_str()))
			return EErrorCode::DBArgumentError;
	}

	if (!command->Execute(queryData.get()))
	{
		LOGE << std::format("Cid {} Model #{} Seq {} failed to execute {}. ReturnValue [{}]",
			container->Cid, container->ModelId, container->Seq, container->ProcedureName, command->GetReturnValue());
		return EErrorCode::DBError;
	}

	LOGD << std::format("Cid {} Model #{} Seq {} has executed [{}]",
		container->Cid, container->ModelId, container->Seq, container->ProcedureName);

	if (command->GetReturnValue() == 49999)
	{
		LOGE << std::format("Cid {} CurrentSeq {} AttemptedSeq {}",
			container->Cid, NSStorageManager::GetInstance()->GetUpdatedSequence(container->Cid), container->Seq);
		return EErrorCode::DBSequenceUpdateError;
	}

	return EErrorCode::None;
}

EErrorCode NSStorageUpdater::ProcessSequence(const std::shared_ptr<NSStorageUpdateContainer> container)
{
	if (container->Session == nullptr || container->Session->IsClosed() || container->Session->IsClosing())
	{
		// relay to another game servers
#ifdef _DEBUG
		LOGD << std::format("NSStorageUpdater::ProcessSequence() session is not exists. relay the event to other processes...");
#endif
	}

	if (!NSStorageManager::GetInstance()->SetUpdatedSequence(container->Cid, container->Seq))
		return EErrorCode::DBSequenceUpdateError;

	return EErrorCode::None;
}

EErrorCode NSStorageUpdater::ResultFunc(const std::shared_ptr<NSQueryData> queryData, const std::shared_ptr<NSStorageUpdateContainer> container)
{
	int32_t returnValue = queryData->GetReturnValue();
	EErrorCode executeError = queryData->GetErrorCode();
	if (executeError != EErrorCode::None)
	{
		switch (executeError)
		{
		case EErrorCode::DBConnectionError:
		{
			NSStorageManager::GetInstance()->AddFailCount(container->Cid);
			PostQueryDelay(1000, container);
		}
		break;
		case EErrorCode::AdoCommandNullptr:
		{
			DisconnectSession(container->Session, executeError);
		}
		break;
		case EErrorCode::DBArgumentError:
		{
			DisconnectSession(container->Session, executeError);
		}
		break;
		case EErrorCode::DBError:
		{
			// retry? error?
			// disconnect test
			DisconnectSession(container->Session, executeError);
		}
		break;
		case EErrorCode::DBSequenceUpdateError:
		{
			DisconnectSession(container->Session, executeError);
		}
		break;
		}

		return executeError;
	}

	if (returnValue != 0)
	{
		LOGE << std::format("Cid {} Model #{} Seq {} found error return. [{}]",
			container->Cid, container->ModelId, container->Seq, returnValue);
		return EErrorCode::DBError;
	}

	ProcessSequence(container);

	if (!container->Callbacks.empty())
	{
		for (auto& callback : container->Callbacks)
		{
			callback(queryData, container.get());
		}
	}

	return EErrorCode::None;
}

void NSStorageUpdater::DisconnectSession(std::shared_ptr<NSDBSession> session, EErrorCode error)
{
	if (session != nullptr && session->GetAID() != 0)
	{
		session->SendSystemNtfThenClose(error);
	}
}
#ifdef _DEBUG
void NSStorageUpdater::SetCommitCalled()
{
	m_CommitCalled = true;
}
#endif	