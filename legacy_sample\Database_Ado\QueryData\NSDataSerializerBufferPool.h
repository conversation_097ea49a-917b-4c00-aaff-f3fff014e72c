#pragma once

#include <concurrent_queue.h>

#include "NSSingleton.h"

class NSDataSerializerBufferBase
{
public:
	NSDataSerializerBufferBase(const NSDataSerializerBufferBase&) = delete;
	NSDataSerializerBufferBase& operator=(const NSDataSerializerBufferBase&) = delete;

	NSDataSerializerBufferBase(NSDataSerializerBufferBase&& rhs) noexcept;
	NSDataSerializerBufferBase& operator=(NSDataSerializerBufferBase&& rhs) noexcept;

	NSDataSerializerBufferBase() = default;
	NSDataSerializerBufferBase(int32_t iSize);
	~NSDataSerializerBufferBase();

public:
	int32_t GetMaxSize()
	{
		return m_MaxSize;
	}

	void SetSize(int32_t iSize)
	{
		m_Size = iSize;
	}

	int32_t GetSize()
	{
		return m_Size;
	}

	void CopyBuffer(char* pcBuffer, int iSize)
	{
		if (m_MaxSize < iSize)
		{
			// assert(false);
			return;
		}

		memcpy(m_Buffer.get(), pc<PERSON>uffer, iSize);
		m_Size = iSize;
	}

	char* GetBuffer()
	{
		return m_Buffer.get();
	}

private:
	std::unique_ptr<char[]> m_Buffer;
	int32_t m_MaxSize = -1;
	int32_t m_Size = -1;
};

#define DEF_DATABASE_BUFFER_CNT 2048
#define DEF_DATABASE_MIN_SIZE 16
