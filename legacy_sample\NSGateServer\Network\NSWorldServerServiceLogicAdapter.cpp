#include "stdafx.h"
#include "NSWorldServerServiceLogicAdapter.h"
#include "NSWorldServerHandler.h"

void NSWorldServerServiceLogicAdapter::ForwardAcceptEventIntoThis(const std::shared_ptr<CNLIOInterfaceAdapter>& socketInterface)
{
	NSWorldServerHandler::GetInstance()->ForwardedEventAccept(socketInterface);
}

void NSWorldServerServiceLogicAdapter::ForwardRecvPacketEventIntoThis(int64_t socketInterfaceId, const char* buffer, int32_t size, bool allocateForCopy)
{
	NSWorldServerHandler::GetInstance()->ForwardedEventRecvPacket(socketInterfaceId, buffer, size, allocateForCopy);
}
void NSWorldServerServiceLogicAdapter::ForwardCloseEventIntoThis(int64_t socketInterfaceId, int32_t nativeErrorCode, const std::string& why)
{
	NSWorldServerHandler::GetInstance()->ForwardedEventClose(socketInterfaceId, nativeErrorCode, why);
}

void NSWorldServerServiceLogicAdapter::ForwardConnectSuccessEventIntoThis(const std::shared_ptr<CNLIOInterfaceAdapter>& socketInterface, const std::string& ip, unsigned short port)
{
	NSWorldServerHandler::GetInstance()->ForwardedEventConnectSuccess(socketInterface, ip, port);
}

void NSWorldServerServiceLogicAdapter::ForwardConnectFailEventIntoThis(const std::shared_ptr<CNLIOInterfaceAdapter>& socketInterface, const std::string& ip, unsigned short port, int32_t nativeErrorCode, const std::string& why)
{
	NSWorldServerHandler::GetInstance()->ForwardedEventConnectFail(socketInterface, ip, port, nativeErrorCode, why);
}
