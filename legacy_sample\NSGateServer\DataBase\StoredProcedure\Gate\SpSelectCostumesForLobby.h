#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpSelectCostumesForLobby : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spSelectCostumesForLobby";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);

public:
	struct Input
	{
		int64_t AID = 0;
	} Input;

	SpSelectCostumesForLobby() = default;
	SpSelectCostumesForLobby(const int64_t AID);
};
