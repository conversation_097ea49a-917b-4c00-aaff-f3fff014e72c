#pragma once

#include "NSSingleton.h"

class NSPacketData;
class NSClientSession;
class NSGateModule;
class NSGatePacketRouter : public TemplateSingleton<NSGatePacketRouter>
{
public:
	NSGatePacketRouter();
	~NSGatePacketRouter() override;

	void Init();
	bool AddSession(std::shared_ptr<NSClientSession>& session, NSGateModule* gameModule);
	bool MoveSession(std::shared_ptr<NSClientSession>& session, EGateModule moduleType);

	bool PushPacket(NSPacketData* packetData);
	bool HandlePacket(NSPacketData* packetData);

	bool MoveLoginModule(std::shared_ptr<NSClientSession>& session);

	void CloseSession(const int64_t channelId);

private:
	std::unordered_map<int64_t, NSGateModule*> m_GateModuleByChannelId; //채널아이디에 맵핑된 모듈
};
