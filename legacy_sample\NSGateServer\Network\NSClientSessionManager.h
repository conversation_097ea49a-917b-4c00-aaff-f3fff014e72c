#pragma once

#include "NSSingleton.h"
#include "NSClientSession.h"
#include "NPModels.h"
#include "NPDefineEnum.h"
class NSClientSessionManager : public TemplateSingleton<NSClientSessionManager>
{
public:
	bool AddSession(std::shared_ptr<NSClientSession>& session);
	auto GetSessionSize() const noexcept->size_t;
	void CloseSession(int64_t clientChannelID);
	void CleanZombie();
	void CloseAllSession();
	void CloseAllSession(int64_t worldServerChannelID);
	bool UpdateSessionAID(std::shared_ptr<NSClientSession>& session);
	void UpdateActiveSessionId(std::shared_ptr<NSClientSession>& session);
	void UpdateUnActiveChannelId(std::shared_ptr<NSClientSession>& session);

	auto RemoveSession(int64_t channelId)->std::shared_ptr<NSClientSession>;
	auto GetSessionByChannelId(int64_t channelId)->std::shared_ptr<NSClientSession>;
	auto GetSessionBySessionId(int64_t sessionId)->std::shared_ptr<NSClientSession>;
	auto GetSessionByAID(int64_t aid)->std::shared_ptr<NSClientSession>;	
	auto GetSessionByActiveSessionId(const std::string& pid) -> std::shared_ptr<NSClientSession>;

	void BroadcastMessage(std::string message, uint32_t duration);
private:
	std::unordered_map<int64_t, std::shared_ptr<NSClientSession>> m_SessionByChannelId;
	std::unordered_map<int64_t, std::shared_ptr<NSClientSession>> m_SessionBySessionId;
	std::unordered_map<int64_t, std::shared_ptr<NSClientSession>> m_SessionByAid;	
	std::unordered_map<int64_t, std::shared_ptr<NSClientSession>> m_SessionByUnActiveChannelId;
	std::unordered_map<std::string, std::shared_ptr<NSClientSession>> m_SessionByActiveSessionId;
};

