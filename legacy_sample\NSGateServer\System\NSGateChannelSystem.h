#pragma once

#include "System/SystemBase/NSGateSystemBase.h"

class NSClientSession;
class NPPacketMoveChannelReq;
class NPPacketGetChannelListReq;
class NSGateChannelSystem : public NSGateSystemBase
{
public:
	constexpr static auto CHECK_VALID_NAME_EXACT = false;

	explicit NSGateChannelSystem(NSGateModule* gameModule);
	virtual ~NSGateChannelSystem() = default;

public:
	virtual bool Init() override;
	virtual bool Reset() override;

	void PacketMoveChannelReq(std::shared_ptr<NSClientSession>& session, NPPacketMoveChannelReq* packet);
	void PacketGetChannelListReq(std::shared_ptr<NSClientSession>& session, NPPacketGetChannelListReq* packet);
};
