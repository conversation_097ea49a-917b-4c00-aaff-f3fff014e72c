#include "../stdafx.h"
#include "AsyncDBWorker.h"
#include "../ConnectionPool/NSMySQLConnectionPool.h"
#include "../Connection/NSMySQLConnection.h"
#include <algorithm>

AsyncDBWorker::AsyncDBWorker()
{
}

AsyncDBWorker::~AsyncDBWorker()
{
    Finalize();
}

bool AsyncDBWorker::Initialize(int threadIndex, NSMySQLConnectionPool* pool)
{
    if (m_running)
        return false;

    m_threadIndex = threadIndex;
    m_pool = pool;
    m_running = true;

    // 전용 커넥션 획득
    m_connection = m_pool->GetConnection(threadIndex);
    if (!m_connection)
        return false;

    // 워커 스레드 시작
    m_workerThread = std::thread([this]() { ProcessLoop(); });

    return true;
}

void AsyncDBWorker::Finalize()
{
    if (!m_running)
        return;

    m_running = false;

    if (m_workerThread.joinable())
    {
        m_workerThread.join();
    }

    // 모든 캐시된 Statement 정리
    {
        std::lock_guard<std::mutex> lock(m_stmtCacheMutex);
        for (auto& [procName, stmts] : m_stmtCache)
        {
            for (auto stmt : stmts)
            {
                if (stmt)
                {
                    try
                    {
                        mysql_stmt_close(stmt);
                    }
                    catch (const std::exception& e)
                    {
                        LOGE << "Failed to close statement for procedure: " << procName 
                             << ", Error: " << e.what();
                    }
                }
            }
            stmts.clear();
        }
        m_stmtCache.clear();
    }

    // 커넥션 반환
    if (m_connection && m_pool)
    {
        m_pool->ReturnConnection(std::move(m_connection), m_threadIndex);
    }
}

void AsyncDBWorker::PostWork(WorkItem work)
{
    {
        std::lock_guard<std::mutex> lock(m_queueMutex);
        m_cidQueues[work.cid].pending.push(std::move(work));
        m_totalPending++;
    }
}

void AsyncDBWorker::ProcessLoop()
{
    // 스레드 이름 설정
    std::string threadName = "AsyncDBWorker#" + std::to_string(m_threadIndex);
#ifdef _WIN32
    const DWORD MS_VC_EXCEPTION = 0x406D1388;
    #pragma pack(push,8)
    typedef struct tagTHREADNAME_INFO
    {
        DWORD dwType;
        LPCSTR szName;
        DWORD dwThreadID;
        DWORD dwFlags;
    } THREADNAME_INFO;
    #pragma pack(pop)

    THREADNAME_INFO info;
    info.dwType = 0x1000;
    info.szName = threadName.c_str();
    info.dwThreadID = GetCurrentThreadId();
    info.dwFlags = 0;

    __try
    {
        RaiseException(MS_VC_EXCEPTION, 0, sizeof(info)/sizeof(ULONG_PTR), (ULONG_PTR*)&info);
    }
    __except(EXCEPTION_EXECUTE_HANDLER)
    {
    }
#endif

    while (m_running)
    {
        // 1. 각 CID 큐에서 실행 가능한 작업 시작
        std::vector<int64_t> cidsToProcess;
        {
            std::lock_guard<std::mutex> lock(m_queueMutex);
            // 먼저 처리할 CID들을 수집
            for (const auto& [cid, queue] : m_cidQueues)
            {
                if (!queue.isExecuting && !queue.pending.empty())
                {
                    cidsToProcess.push_back(cid);
                }
            }
        }
        
        // 락 밖에서 작업 시작 (반복자 무효화 방지)
        for (int64_t cid : cidsToProcess)
        {
            std::lock_guard<std::mutex> lock(m_queueMutex);
            auto it = m_cidQueues.find(cid);
            if (it != m_cidQueues.end() && !it->second.isExecuting && !it->second.pending.empty())
            {
                StartNextWork(cid, it->second);
            }
        }

        // 2. 진행 중인 비동기 작업들 폴링
        PollActiveOperations();

        // 3. 완료된 작업 처리
        ProcessCompletedOperations();

        // CPU 사용률 조절
        if (m_activeOps.empty())
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
        else
        {
            std::this_thread::yield();
        }
    }
}

void AsyncDBWorker::StartNextWork(int64_t cid, CidQueue& queue)
{
    auto work = std::move(queue.pending.front());
    queue.pending.pop();
    m_totalPending--;

    // PreparedStatement 준비
    MYSQL_STMT* stmt = GetCachedStatement(work.procName);
    if (!stmt)
    {
        // Statement 생성 실패
        work.callback(false, nullptr);
        return;
    }

    // 파라미터 바인딩
    if (work.bindParams)
    {
        work.bindParams(stmt);
    }

    // 비동기 실행 시작
    int status = 0; // 동기 실행으로 시작 (MySQL C API는 비동기를 완전히 지원하지 않음)
    
    if (mysql_stmt_execute(stmt) == 0)
    {
        // 결과 저장
        if (mysql_stmt_store_result(stmt) == 0)
        {
            // 결과 메타데이터 가져오기 (이것은 복사가 아니라 참조)
            MYSQL_RES* metadata = mysql_stmt_result_metadata(stmt);
            
            try
            {
                // 콜백 실행 - metadata는 콜백 내에서 사용되고 관리됨
                work.callback(true, metadata);
            }
            catch (const std::exception& e)
            {
                LOGE << "Callback exception: " << e.what() << " for procedure: " << work.procName;
                // 예외 발생 시 메타데이터 정리
                if (metadata)
                {
                    mysql_free_result(metadata);
                }
            }
            
            // metadata는 RecordSet에서 관리하므로 여기서는 해제하지 않음
            // RecordSet의 소멸자에서 mysql_free_result가 호출됨
        }
        else
        {
            LOGE << "Failed to store result for procedure: " << work.procName 
                 << ", Error: " << mysql_stmt_error(stmt);
            work.callback(false, nullptr);
        }
    }
    else
    {
        LOGE << "Failed to execute statement for procedure: " << work.procName 
             << ", Error: " << mysql_stmt_error(stmt);
        work.callback(false, nullptr);
    }

    // Statement 반환
    ReleaseCachedStatement(work.procName, stmt);
    
    queue.isExecuting = false;
    m_totalProcessed++;
}

void AsyncDBWorker::PollActiveOperations()
{
    // MySQL C API는 진정한 비동기를 지원하지 않으므로
    // 이 함수는 현재 구현에서는 사용되지 않음
}

void AsyncDBWorker::ProcessCompletedOperations()
{
    // MySQL C API는 진정한 비동기를 지원하지 않으므로
    // 이 함수는 현재 구현에서는 사용되지 않음
}

MYSQL_STMT* AsyncDBWorker::GetCachedStatement(const std::string& procName)
{
    std::lock_guard<std::mutex> lock(m_stmtCacheMutex);
    
    auto& stmts = m_stmtCache[procName];
    if (!stmts.empty())
    {
        auto stmt = stmts.back();
        stmts.pop_back();
        return stmt;
    }

    // 새 Statement 생성
    if (!m_connection)
        return nullptr;

    MYSQL* mysql = m_connection->GetRawConnection();
    if (!mysql)
        return nullptr;

    MYSQL_STMT* stmt = mysql_stmt_init(mysql);
    if (!stmt)
        return nullptr;

    // 프로시저 호출 준비
    std::string query = "CALL " + procName;
    if (mysql_stmt_prepare(stmt, query.c_str(), query.length()) != 0)
    {
        mysql_stmt_close(stmt);
        return nullptr;
    }

    return stmt;
}

void AsyncDBWorker::ReleaseCachedStatement(const std::string& procName, MYSQL_STMT* stmt)
{
    if (!stmt)
        return;

    std::lock_guard<std::mutex> lock(m_stmtCacheMutex);
    
    auto& stmts = m_stmtCache[procName];
    if (stmts.size() < 10)  // 최대 10개까지 캐시
    {
        mysql_stmt_reset(stmt);
        stmts.push_back(stmt);
    }
    else
    {
        mysql_stmt_close(stmt);
    }
}