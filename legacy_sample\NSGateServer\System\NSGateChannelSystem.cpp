#include "stdafx.h"
#include "NSGateChannelSystem.h"

#include "Network/NSClientSession.h"
#include "Module/NSGateModule.h"

#include "DataBase/StoredProcedure/NSStoredProcedureBatch.h"

#include "NPModels.h"
#include "NSTickManager/NSTickManager.h"

#include "Network/NSGatePacketRouter.h"

NSGateChannelSystem::NSGateChannelSystem(NSGateModule* gameModule)
	: NSGateSystemBase(gameModule)
{
}

bool NSGateChannelSystem::Init()
{
	constexpr auto InputIntervalMS = 200;

	INSERT_PROCESSOR_INTERVAL(NSGateChannelSystem::PacketMoveChannelReq, NPPacketMoveChannelReq, InputIntervalMS);
	INSERT_PROCESSOR_INTERVAL(NSGateChannelSystem::PacketGetChannelListReq, NPPacketGetChannelListReq, InputIntervalMS);

	return true;
}

bool NSGateChannelSystem::Reset()
{
	return true;
}

void NSGateChannelSystem::PacketMoveChannelReq(std::shared_ptr<NSClientSession>& session, NPPacketMoveChannelReq* packet)
{
	//서버가 없음
	auto pcServer = NSWorldServerManager::GetInstance()->GetWorldServer(packet->GetChannelSid());
	if (pcServer == nullptr)
		return;

	//서버가 꺼져있음
	if (!pcServer->IsWorldServerActive())
		return;

	//월드 서버 타입이 퍼시스턴트가 아님
	if ((pcServer->GetServerInfo().m_WorldServerType & EWorldServerType::Persistant) == EWorldServerType::None)
		return;

	NSPacketMoveChannelReq req;
	req.SetCID(session->GetCID());
	req.SetChannelSid(packet->GetChannelSid());
	session->WorldServerSend(&req);
}

void NSGateChannelSystem::PacketGetChannelListReq(std::shared_ptr<NSClientSession>& session, NPPacketGetChannelListReq*)
{
	NPPacketGetChannelListAck ack;

	int32_t channelIndex = -1;
	for (auto& pcServer : NSWorldServerManager::GetInstance()->GetWorldServerList(EWorldServerTypeIndex::Persistant))
	{
		++channelIndex;
		//꺼져있는 서버는 보내지 않음, 인덱스는 유지
		if (!pcServer->IsWorldServerActive())
			continue;

		NPChannelInfo channelInfo{
			.ChannelIndex = channelIndex,
			.ChannelSid = pcServer->GetServerInfo().m_Sid
		};
		ack.AddChannelList(channelInfo);
	}
	session->Send(ack);
}