#pragma once
#include <memory>
#include <atomic>

// 게임 세션 인터페이스 (레거시 호환)
class NSDBSession : public std::enable_shared_from_this<NSDBSession>
{
public:
    NSDBSession() = default;
    virtual ~NSDBSession() = default;

    // 계정 ID
    virtual int64_t GetAID() const = 0;
    
    // 캐릭터 ID
    virtual int64_t GetCID() const = 0;
    
    // 세션 상태
    virtual bool IsClosing() const = 0;
    virtual bool IsClosed() const = 0;
    
    // 에러 발생 시 클라이언트에 알림 후 종료
    virtual void SendSystemNtfThenClose(int errorCode) = 0;
    
    // 샤딩용 키 가져오기
    uint64_t GetShardKey() const { return GetAID(); }
};

// 더미 세션 (테스트용)
class NSDummySession : public NSDBSession
{
public:
    NSDummySession(int64_t aid = 0, int64_t cid = 0) 
        : m_aid(aid), m_cid(cid) {}
    
    int64_t GetAID() const override { return m_aid; }
    int64_t GetCID() const override { return m_cid; }
    bool IsClosing() const override { return m_closing; }
    bool IsClosed() const override { return m_closed; }
    
    void SendSystemNtfThenClose(int errorCode) override 
    {
        // 실제 구현에서는 네트워크로 에러 전송
        m_closing = true;
        m_closed = true;
    }

private:
    int64_t m_aid = 0;
    int64_t m_cid = 0;
    std::atomic<bool> m_closing{false};
    std::atomic<bool> m_closed{false};
};