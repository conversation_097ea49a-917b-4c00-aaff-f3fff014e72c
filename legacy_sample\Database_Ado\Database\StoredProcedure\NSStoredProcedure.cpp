#include "stdafx.h"
#include "NSStoredProcedure.h"
#include "DataBase/NSDataBaseManager.h"
#include "DataBase/Storage/NSStorageUpdater.h"

EErrorCode NSStoredProcedure::QueryFunc(const std::shared_ptr<NSQueryData> queryData)
{
	EErrorCode errorCode = EErrorCode::None;

	const auto procedureHost = GetProcedureHost();

	auto connection = NSDataBaseManager::GetInstance()->GetDBConnection(procedureHost);
	if (connection == nullptr)
		return EErrorCode::DBConnectionError;

	NSAdoCommand* command = nullptr;
	command = connection->GetCommand(GetProcedureName());
	if (command == nullptr)
		return EErrorCode::AdoCommandNullptr;

	errorCode = MakeQuery(command);
	if (errorCode != EErrorCode::None)
	{
		LOGE << std::format("{} make query failed. EErrorCode [ {} ]", GetProcedureName(), (uint32_t)errorCode);
		return errorCode;
	}

	if (!command->Execute(queryData.get()))
	{
		LOGE << std::format("{} execute failed. ReturnValue [ {} ]", GetProcedureName(), command->GetReturnValue());
		return EErrorCode::DBError;
	}

	m_ReturnValue = command->GetReturnValue();
	if (!IsValid())
	{
		EErrorCode returnError = HandleReturnValue(m_ReturnValue);
		LOGE << std::format("{} returns an error. spReturn [ {} ] -> EErrorCode [ {} ]",
			GetProcedureName(), m_ReturnValue, (uint32_t)returnError);
		return returnError;
	}

	errorCode = MakeOutput(command);
	if (errorCode != EErrorCode::None)
	{
		LOGE << std::format("{} make output failed. EErrorCode [ {} ]", GetProcedureName(), (uint32_t)errorCode);
		return errorCode;
	}

	return EErrorCode::None;
}

EErrorCode NSStoredProcedure::StorageQueryFunc(const std::shared_ptr<NSQueryData> queryData, const std::shared_ptr<NSStorageUpdateContainer> container)
{
	EErrorCode errorCode = EErrorCode::None;

	const auto procedureHost = GetProcedureHost();

	auto connection = NSDataBaseManager::GetInstance()->GetDBConnection(procedureHost);
	if (connection == nullptr)
		return EErrorCode::DBConnectionError;

	NSAdoCommand* command = nullptr;
	command = connection->GetCommand(GetProcedureName());
	if (command == nullptr)
		return EErrorCode::AdoCommandNullptr;

	container->ProcedureName = GetProcedureName();

	errorCode = MakeStorageQuery(command, container.get());
	if (errorCode != EErrorCode::None)
		return errorCode;

	if (!command->Execute(queryData.get()))
	{
		LOGE << std::format("{} execute failed. ReturnValue [ {} ]", GetProcedureName(), command->GetReturnValue());
		return EErrorCode::DBError;
	}

	m_ReturnValue = command->GetReturnValue();
	if (!IsValid())
		return HandleReturnValue(m_ReturnValue);

	errorCode = MakeOutput(command);
	if (errorCode != EErrorCode::None)
		return errorCode;

	return EErrorCode::None;
}

bool NSInput_HasReward::IsVaildRewardInput() const
{
	return (memcmp(RewardPayload, &MAX_REWARD_PAYLOAD_OVERFLOW_FLAG, sizeof(int)) == 0) ? true : false;
}

void NSInput_HasReward::AssignRewardInput(const char* payload)
{
	if (payload == nullptr)
	{
		LOGD << "RewardPayload is nullptr.";
		memcpy_s(RewardPayload, MAX_REWARD_PAYLOAD_SIZE, "[]", 3);
		return;
	}

	if (strlen(payload) > MAX_REWARD_PAYLOAD_SIZE)
	{
		memcpy_s(RewardPayload, MAX_REWARD_PAYLOAD_SIZE, &MAX_REWARD_PAYLOAD_OVERFLOW_FLAG, sizeof(int));
		LOGE << "RewardPayload exceed buffer limit";
		return;
	}
	memcpy_s(RewardPayload, MAX_REWARD_PAYLOAD_SIZE, payload, strlen(payload) + 1);
}

EErrorCode NSInput_HasReward::SetRewardInput([[maybe_unused]] const int64_t aid, [[maybe_unused]] const int64_t cid, NSAdoCommand* command, const char* payloadField)
{
	if (!command->SetItem(payloadField, RewardPayload))
		return EErrorCode::DBArgumentError;
	return EErrorCode::None;
}

void NSOutput_HasReward::GetRewardOutput(NSAdoCommand* command)
{
	command->GetItem(PARAMETER_TIMESTAMP_OUTPUT, ResultTimestamp);
	command->GetItem(PARAMETER_REWARD_OUTPUT, ResultReward, MAX_REWARD_OUTPUT_SIZE);
}

void NSInput_HasConsume::AssignConsumeInput(const char* payload)
{
	if (payload == nullptr)
	{
		LOGD << "ConsumePayload is nullptr.";
		memcpy_s(ConsumePayload, MAX_CONSUME_PAYLOAD_SIZE, "[]", 3);
		return;
	}

	if (strlen(payload) > MAX_CONSUME_PAYLOAD_SIZE)
	{
		LOGE << "ConsumePayload exceed buffer limit";
		return;
	}
	memcpy_s(ConsumePayload, MAX_CONSUME_PAYLOAD_SIZE, payload, strlen(payload) + 1);
}

EErrorCode NSInput_HasConsume::SetConsumeInput([[maybe_unused]] const int64_t aid, [[maybe_unused]] const int64_t cid, NSAdoCommand* command)
{
	if (!command->SetItem(PARAMETER_CONSUME_INPUT, ConsumePayload))
		return EErrorCode::DBArgumentError;
	return EErrorCode::None;
}

void NSOutput_HasConsume::GetConsumeOutput(NSAdoCommand* command)
{
	command->GetItem(PARAMETER_TIMESTAMP_OUTPUT, ResultTimestamp);
	command->GetItem(PARAMETER_CONSUME_OUTPUT, ResultConsume, MAX_CONSUME_OUTPUT_SIZE);
}

void NSInput_HasConsumeReward::AssignInputs(const char* consume, const char* reward)
{
	AssignConsumeInput(consume);
	AssignRewardInput(reward);
}

EErrorCode NSInput_HasConsumeReward::SetInputs(const int64_t aid, const int64_t cid, NSAdoCommand* command)
{
	auto consumeError = SetConsumeInput(aid, cid, command);
	if (consumeError != EErrorCode::None)
		return consumeError;

	auto rewardError = SetRewardInput(aid, cid, command);
	if (rewardError != EErrorCode::None)
		return rewardError;

	return EErrorCode::None;
}

void NSOutput_HasConsumeReward::GetOutputs(NSAdoCommand* command)
{
	GetConsumeOutput(command);
	GetRewardOutput(command);
}
