#pragma once
#include <string>
#include <chrono>
#include <sstream>
#include <iomanip>
#include <atomic>
#include <thread>

// 구조화된 데이터베이스 로깅 시스템
// 쿼리 추적, 성능 모니터링, 에러 분석을 위한 통합 로깅

namespace DatabaseLogger {

// 쿼리 ID 생성기
inline std::string GenerateQueryId() {
    static std::atomic<uint64_t> counter{0};
    auto now = std::chrono::system_clock::now();
    auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>
                    (now.time_since_epoch()).count();
    
    // 포맷: Q_타임스탬프_카운터 (예: Q_1737526400000_12345)
    return "Q_" + std::to_string(timestamp) + "_" + 
           std::to_string(counter.fetch_add(1));
}

// 현재 스레드 정보 가져오기
inline std::string GetThreadInfo() {
    std::stringstream ss;
    ss << "T" << std::this_thread::get_id();
    return ss.str();
}

// 타임스탬프 포맷팅
inline std::string GetTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>
             (now.time_since_epoch()) % 1000;
    
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << '.' << std::setfill('0') << std::setw(3) << ms.count();
    return ss.str();
}

// 경과 시간 계산
class ElapsedTimer {
private:
    std::chrono::steady_clock::time_point m_start;
    
public:
    ElapsedTimer() : m_start(std::chrono::steady_clock::now()) {}
    
    uint64_t GetElapsedMs() const {
        auto now = std::chrono::steady_clock::now();
        return std::chrono::duration_cast<std::chrono::milliseconds>
               (now - m_start).count();
    }
};

} // namespace DatabaseLogger

// ===== 구조화된 로그 매크로 =====

// 쿼리 시작 로그
#define DB_LOG_QUERY_START(queryId, procName, cid, threadIndex) \
    LOGD << "[QUERY_START]" \
         << " id=" << queryId \
         << " proc=" << procName \
         << " cid=" << cid \
         << " thread=" << threadIndex \
         << " time=" << DatabaseLogger::GetTimestamp()

// 쿼리 종료 로그 (성공)
#define DB_LOG_QUERY_END(queryId, success, elapsed) \
    LOGD << "[QUERY_END]" \
         << " id=" << queryId \
         << " success=" << (success ? "true" : "false") \
         << " elapsed=" << elapsed << "ms"

// 쿼리 에러 로그
#define DB_LOG_ERROR(queryId, error, context) \
    LOGE << "[QUERY_ERROR]" \
         << " id=" << queryId \
         << " error=\"" << error << "\"" \
         << " " << context

// 연결 풀 로그
#define DB_LOG_POOL_GET(poolType, shardId, threadIndex, success) \
    LOGD << "[POOL_GET]" \
         << " pool=" << poolType << "_" << shardId \
         << " thread=" << threadIndex \
         << " success=" << (success ? "true" : "false")

#define DB_LOG_POOL_RETURN(poolType, shardId, threadIndex) \
    LOGD << "[POOL_RETURN]" \
         << " pool=" << poolType << "_" << shardId \
         << " thread=" << threadIndex

// 느린 쿼리 로그
#define DB_LOG_SLOW_QUERY(queryId, procName, elapsed, threshold) \
    LOGW << "[SLOW_QUERY]" \
         << " id=" << queryId \
         << " proc=" << procName \
         << " elapsed=" << elapsed << "ms" \
         << " threshold=" << threshold << "ms"

// 데드락 로그
#define DB_LOG_DEADLOCK(queryId, procName, retryCount) \
    LOGW << "[DEADLOCK]" \
         << " id=" << queryId \
         << " proc=" << procName \
         << " retry=" << retryCount

// 트랜잭션 로그
#define DB_LOG_TRANSACTION_BEGIN(transactionId, cid) \
    LOGD << "[TRANSACTION_BEGIN]" \
         << " tid=" << transactionId \
         << " cid=" << cid

#define DB_LOG_TRANSACTION_END(transactionId, action) \
    LOGD << "[TRANSACTION_END]" \
         << " tid=" << transactionId \
         << " action=" << action  // "commit" or "rollback"

// 성능 통계 로그
#define DB_LOG_STATS(activeQueries, totalQueries, failedQueries, avgTime) \
    LOGI << "[DB_STATS]" \
         << " active=" << activeQueries \
         << " total=" << totalQueries \
         << " failed=" << failedQueries \
         << " avg_time=" << avgTime << "ms"

// CID 라우팅 로그 (디버그용)
#ifdef _DEBUG
#define DB_LOG_CID_ROUTING(cid, threadIndex, reason) \
    LOGD << "[CID_ROUTING]" \
         << " cid=" << cid \
         << " thread=" << threadIndex \
         << " reason=\"" << reason << "\""
#else
#define DB_LOG_CID_ROUTING(cid, threadIndex, reason) ((void)0)
#endif

// 쿼리 단계별 추적 (디버그용)
#ifdef _DEBUG
#define DB_LOG_QUERY_STEP(queryId, step) \
    LOGD << "[QUERY_STEP]" \
         << " id=" << queryId \
         << " step=\"" << step << "\""
#else
#define DB_LOG_QUERY_STEP(queryId, step) ((void)0)
#endif

// ===== 로그 파싱을 위한 구조화된 컨텍스트 =====

class QueryLogContext {
public:
    std::string queryId;
    std::string procName;
    int64_t cid;
    int threadIndex;
    DatabaseLogger::ElapsedTimer timer;
    
    QueryLogContext(const std::string& proc, int64_t c, int thread)
        : queryId(DatabaseLogger::GenerateQueryId())
        , procName(proc)
        , cid(c)
        , threadIndex(thread)
    {
        DB_LOG_QUERY_START(queryId, procName, cid, threadIndex);
    }
    
    ~QueryLogContext() {
        // 소멸자에서 자동으로 종료 로그
        if (!logged) {
            LogEnd(true);
        }
    }
    
    void LogEnd(bool success) {
        if (!logged) {
            auto elapsed = timer.GetElapsedMs();
            DB_LOG_QUERY_END(queryId, success, elapsed);
            
            // 느린 쿼리 체크
            if (elapsed > 1000) { // 1초 이상
                DB_LOG_SLOW_QUERY(queryId, procName, elapsed, 1000);
            }
            logged = true;
        }
    }
    
    void LogError(const std::string& error, const std::string& context = "") {
        DB_LOG_ERROR(queryId, error, context);
        LogEnd(false);
    }
    
    void LogStep(const std::string& step) {
        DB_LOG_QUERY_STEP(queryId, step);
    }
    
private:
    bool logged = false;
};