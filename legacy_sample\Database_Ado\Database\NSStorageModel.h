#pragma once

#define DECLARE_MODEL_UTIL \
public: \
	virtual const int32_t GetModelId() const override { return GetTypeId(); } \
	virtual const char* GetModelName() const override { return strComponentName; } \
	virtual const char* GetSelectName() const override { return spSelect; } \
	virtual const char* GetUpsertName() const override { return spUpsert; }

#define DECLARE_ESCROW_UTIL \
public: \
	virtual const char* GetEscrowDepositName() const override { return spDeposit; } \
	virtual const char* GetEscrowWithdrawName() const override { return spWithdraw; } \
	virtual const char* GetEscrowReclaimName() const override { return spReclaim; }



class NSStorageModel
{
public:
	virtual auto GetModelId() const->const int32_t = 0;
	virtual auto GetModelName() const->const char* = 0;
	virtual auto GetSelectName() const->const char* = 0;
	virtual auto GetUpsertName() const->const char* = 0;
	virtual void Serialize(OUT std::vector<std::string>& payloads) = 0;
	virtual void SerializeAffected(OUT std::vector<std::string>& payloads) = 0;
	virtual void Rollback() = 0;
};

class NSStorageEscrowModel : public NSStorageModel
{
public:
	virtual auto GetEscrowDepositName() const->const char* = 0;
	virtual auto GetEscrowWithdrawName() const->const char* = 0;
	virtual auto GetEscrowReclaimName() const->const char* = 0;
	virtual void SerializeEscrow(OUT std::string& payload, const int64_t receiverCid) = 0;
};
