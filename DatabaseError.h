#pragma once
#include <string>
#include <exception>
#include <chrono>
#include <sstream>
#include <mysql.h>
#include <memory>
#include <vector>

// Windows 스택 트레이스
#ifdef _WIN32
#include <windows.h>
#include <dbghelp.h>
#pragma comment(lib, "dbghelp.lib")
#endif

// 상세한 데이터베이스 에러 정보를 담는 클래스
class DatabaseError : public std::exception {
public:
    enum class ErrorCode {
        None = 0,
        ConnectionFailed = 1001,
        ConnectionLost = 1002,
        QueryTimeout = 1003,
        DeadlockDetected = 1004,
        DuplicateKey = 1005,
        InvalidParameter = 2001,
        PrepareStatementFailed = 2002,
        ExecuteFailed = 2003,
        ResultFetchFailed = 2004,
        TransactionFailed = 3001,
        PoolExhausted = 4001,
        Unknown = 9999
    };

    // 에러 컨텍스트 정보
    struct ErrorContext {
        ErrorCode code = ErrorCode::None;
        std::string message;
        std::string mysqlError;
        int mysqlErrno = 0;
        std::string sqlState;
        std::string query;
        std::string queryId;
        std::string procName;
        int64_t cid = 0;
        int threadIndex = -1;
        std::string stackTrace;
        std::chrono::milliseconds executionTime{0};
        std::chrono::system_clock::time_point timestamp;
        
        std::string ToString() const {
            std::stringstream ss;
            ss << "\n========== Database Error Details ==========\n";
            ss << "Error Code: " << static_cast<int>(code) 
               << " (" << GetErrorCodeString(code) << ")\n";
            ss << "Message: " << message << "\n";
            
            if (!mysqlError.empty()) {
                ss << "MySQL Error: " << mysqlError 
                   << " (errno: " << mysqlErrno 
                   << ", SQLSTATE: " << sqlState << ")\n";
            }
            
            if (!queryId.empty()) {
                ss << "Query ID: " << queryId << "\n";
            }
            
            if (!procName.empty()) {
                ss << "Procedure: " << procName << "\n";
            }
            
            if (!query.empty()) {
                ss << "Query: " << query << "\n";
            }
            
            if (cid != 0) {
                ss << "CID: " << cid << "\n";
            }
            
            if (threadIndex >= 0) {
                ss << "Thread Index: " << threadIndex << "\n";
            }
            
            if (executionTime.count() > 0) {
                ss << "Execution Time: " << executionTime.count() << "ms\n";
            }
            
            ss << "Timestamp: " << FormatTimestamp(timestamp) << "\n";
            
            if (!stackTrace.empty()) {
                ss << "Stack Trace:\n" << stackTrace << "\n";
            }
            
            ss << "==========================================\n";
            return ss.str();
        }
        
        // JSON 형식으로 변환 (로그 파싱용)
        std::string ToJson() const {
            std::stringstream ss;
            ss << "{";
            ss << "\"error_code\":" << static_cast<int>(code) << ",";
            ss << "\"error_type\":\"" << GetErrorCodeString(code) << "\",";
            ss << "\"message\":\"" << EscapeJson(message) << "\",";
            
            if (!mysqlError.empty()) {
                ss << "\"mysql_error\":\"" << EscapeJson(mysqlError) << "\",";
                ss << "\"mysql_errno\":" << mysqlErrno << ",";
                ss << "\"sql_state\":\"" << sqlState << "\",";
            }
            
            if (!queryId.empty()) {
                ss << "\"query_id\":\"" << queryId << "\",";
            }
            
            if (!procName.empty()) {
                ss << "\"procedure\":\"" << procName << "\",";
            }
            
            if (cid != 0) {
                ss << "\"cid\":" << cid << ",";
            }
            
            if (threadIndex >= 0) {
                ss << "\"thread_index\":" << threadIndex << ",";
            }
            
            if (executionTime.count() > 0) {
                ss << "\"execution_time_ms\":" << executionTime.count() << ",";
            }
            
            ss << "\"timestamp\":\"" << FormatTimestamp(timestamp) << "\"";
            ss << "}";
            
            return ss.str();
        }
        
    private:
        static std::string GetErrorCodeString(ErrorCode code) {
            switch (code) {
                case ErrorCode::None: return "None";
                case ErrorCode::ConnectionFailed: return "ConnectionFailed";
                case ErrorCode::ConnectionLost: return "ConnectionLost";
                case ErrorCode::QueryTimeout: return "QueryTimeout";
                case ErrorCode::DeadlockDetected: return "DeadlockDetected";
                case ErrorCode::DuplicateKey: return "DuplicateKey";
                case ErrorCode::InvalidParameter: return "InvalidParameter";
                case ErrorCode::PrepareStatementFailed: return "PrepareStatementFailed";
                case ErrorCode::ExecuteFailed: return "ExecuteFailed";
                case ErrorCode::ResultFetchFailed: return "ResultFetchFailed";
                case ErrorCode::TransactionFailed: return "TransactionFailed";
                case ErrorCode::PoolExhausted: return "PoolExhausted";
                case ErrorCode::Unknown: return "Unknown";
                default: return "Unknown";
            }
        }
        
        static std::string FormatTimestamp(const std::chrono::system_clock::time_point& tp) {
            auto time_t = std::chrono::system_clock::to_time_t(tp);
            char buffer[100];
            struct tm tm;
            localtime_s(&tm, &time_t);
            strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", &tm);
            return buffer;
        }
        
        static std::string EscapeJson(const std::string& str) {
            std::string result;
            for (char c : str) {
                switch (c) {
                    case '"': result += "\\\""; break;
                    case '\\': result += "\\\\"; break;
                    case '\n': result += "\\n"; break;
                    case '\r': result += "\\r"; break;
                    case '\t': result += "\\t"; break;
                    default: result += c;
                }
            }
            return result;
        }
    };

    // 생성자
    DatabaseError(const ErrorContext& ctx) 
        : m_context(ctx)
        , m_what(ctx.ToString()) 
    {}
    
    // MySQL 에러에서 컨텍스트 생성
    static ErrorContext CreateFromMySQL(
        MYSQL* mysql,
        const std::string& message,
        const std::string& queryId = "",
        const std::string& procName = "",
        int64_t cid = 0,
        int threadIndex = -1)
    {
        ErrorContext ctx;
        ctx.message = message;
        ctx.queryId = queryId;
        ctx.procName = procName;
        ctx.cid = cid;
        ctx.threadIndex = threadIndex;
        ctx.timestamp = std::chrono::system_clock::now();
        
        if (mysql) {
            ctx.mysqlError = mysql_error(mysql);
            ctx.mysqlErrno = mysql_errno(mysql);
            ctx.sqlState = mysql_sqlstate(mysql);
            
            // 에러 코드 매핑
            switch (ctx.mysqlErrno) {
                case 1213: ctx.code = ErrorCode::DeadlockDetected; break;
                case 1062: ctx.code = ErrorCode::DuplicateKey; break;
                case 2006: 
                case 2013: ctx.code = ErrorCode::ConnectionLost; break;
                case 1205: ctx.code = ErrorCode::QueryTimeout; break;
                default: ctx.code = ErrorCode::Unknown;
            }
        } else {
            ctx.code = ErrorCode::ConnectionFailed;
        }
        
        // 스택 트레이스 캡처
        ctx.stackTrace = CaptureStackTrace();
        
        return ctx;
    }
    
    // Statement 에러에서 컨텍스트 생성
    static ErrorContext CreateFromStatement(
        MYSQL_STMT* stmt,
        const std::string& message,
        const std::string& queryId = "",
        const std::string& procName = "",
        int64_t cid = 0,
        int threadIndex = -1)
    {
        ErrorContext ctx;
        ctx.message = message;
        ctx.queryId = queryId;
        ctx.procName = procName;
        ctx.cid = cid;
        ctx.threadIndex = threadIndex;
        ctx.timestamp = std::chrono::system_clock::now();
        
        if (stmt) {
            ctx.mysqlError = mysql_stmt_error(stmt);
            ctx.mysqlErrno = mysql_stmt_errno(stmt);
            ctx.sqlState = mysql_stmt_sqlstate(stmt);
            ctx.code = ErrorCode::ExecuteFailed;
        } else {
            ctx.code = ErrorCode::PrepareStatementFailed;
        }
        
        ctx.stackTrace = CaptureStackTrace();
        
        return ctx;
    }
    
    // what() 오버라이드
    const char* what() const noexcept override {
        return m_what.c_str();
    }
    
    // 컨텍스트 접근자
    const ErrorContext& GetContext() const { return m_context; }
    ErrorCode GetErrorCode() const { return m_context.code; }
    int GetMySQLErrno() const { return m_context.mysqlErrno; }
    
    // 재시도 가능 여부 판단
    bool IsRetryable() const {
        switch (m_context.code) {
            case ErrorCode::DeadlockDetected:
            case ErrorCode::QueryTimeout:
            case ErrorCode::ConnectionLost:
                return true;
            default:
                return false;
        }
    }
    
private:
    ErrorContext m_context;
    std::string m_what;
    
    // 스택 트레이스 캡처
    static std::string CaptureStackTrace() {
#ifdef _WIN32
        std::stringstream ss;
        
        const int MAX_FRAMES = 20;
        void* frames[MAX_FRAMES];
        HANDLE process = GetCurrentProcess();
        
        // 심볼 초기화
        static bool symbolsInitialized = false;
        if (!symbolsInitialized) {
            SymInitialize(process, NULL, TRUE);
            symbolsInitialized = true;
        }
        
        // 스택 프레임 캡처
        WORD frameCount = CaptureStackBackTrace(2, MAX_FRAMES, frames, NULL);
        
        // 각 프레임 정보 출력
        for (WORD i = 0; i < frameCount; i++) {
            DWORD64 address = (DWORD64)(frames[i]);
            DWORD64 displacement = 0;
            
            char buffer[sizeof(SYMBOL_INFO) + MAX_SYM_NAME * sizeof(TCHAR)];
            PSYMBOL_INFO symbol = (PSYMBOL_INFO)buffer;
            symbol->SizeOfStruct = sizeof(SYMBOL_INFO);
            symbol->MaxNameLen = MAX_SYM_NAME;
            
            if (SymFromAddr(process, address, &displacement, symbol)) {
                ss << "  [" << i << "] " << symbol->Name 
                   << " + 0x" << std::hex << displacement << std::dec << "\n";
                
                // 라인 정보 가져오기
                IMAGEHLP_LINE64 line;
                line.SizeOfStruct = sizeof(IMAGEHLP_LINE64);
                DWORD displacement32;
                if (SymGetLineFromAddr64(process, address, &displacement32, &line)) {
                    ss << "       " << line.FileName << ":" << line.LineNumber << "\n";
                }
            } else {
                ss << "  [" << i << "] 0x" << std::hex << address << std::dec << "\n";
            }
        }
        
        return ss.str();
#else
        return "Stack trace not available on this platform";
#endif
    }
};

// 데이터베이스 예외를 던지는 헬퍼 매크로
#define THROW_DB_ERROR(mysql, message) \
    throw DatabaseError(DatabaseError::CreateFromMySQL( \
        mysql, message, "", __FUNCTION__, 0, -1))

#define THROW_STMT_ERROR(stmt, message) \
    throw DatabaseError(DatabaseError::CreateFromStatement( \
        stmt, message, "", __FUNCTION__, 0, -1))

// 컨텍스트와 함께 던지기
#define THROW_DB_ERROR_WITH_CONTEXT(mysql, message, queryId, procName, cid, thread) \
    throw DatabaseError(DatabaseError::CreateFromMySQL( \
        mysql, message, queryId, procName, cid, thread))