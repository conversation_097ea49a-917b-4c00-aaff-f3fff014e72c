#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpCancelDeleteCharacter : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spCancelDeleteCharacter";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);

public:
	struct Input
	{
		int64_t Cid;
	} Input;

	SpCancelDeleteCharacter() = default;
	SpCancelDeleteCharacter(const int64_t cid);
};
