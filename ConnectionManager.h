#pragma once

#include <atomic>
#include <memory>
#include <vector>
#include <string>

namespace Database
{

class NSMySQLConnection;
struct ConnectionInfo;

// 라운드 로빈 방식의 연결 관리자
class ConnectionManager
{
public:
    ConnectionManager();
    ~ConnectionManager();

    bool Initialize(const ConnectionInfo& connInfo, int connectionCount);
    void Shutdown();

    std::shared_ptr<NSMySQLConnection> GetNextConnection();
    
    size_t GetConnectionCount() const { return m_connections.size(); }
    bool IsInitialized() const { return m_initialized; }
    
    // 통계
    uint64_t GetTotalConnectionsUsed() const { return m_totalConnectionsUsed.load(); }
    void GetConnectionStats(std::vector<std::pair<size_t, uint64_t>>& stats) const;

private:
    std::vector<std::shared_ptr<NSMySQLConnection>> m_connections;
    std::atomic<size_t> m_nextConnection{0};
    std::atomic<uint64_t> m_totalConnectionsUsed{0};
    std::atomic<bool> m_initialized{false};
    
    // 연결 생성을 위한 연결 정보
    std::string m_host;
    uint16_t m_port;
    std::string m_user;
    std::string m_password;
    std::string m_database;
    std::string m_charset;
    uint32_t m_connectionFlags;
};

} // namespace Database