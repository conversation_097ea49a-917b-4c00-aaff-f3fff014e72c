#include "stdafx.h"
#include "ErrorLogging.h"
#include "Connection/NSMySQLConnection.h"
#include "MySQLCommand.h"
#include "Threading/NSIOCPWorkManager.h"
#include "NSDataBaseManager.h"
#include <iomanip>
#include <sstream>

void DatabaseErrorLogger::LogError(const ErrorInfo& error)
{
    if (!m_enabled || !m_logConnection)
        return;
    
    ExecuteErrorLogProcedure(error);
}

void DatabaseErrorLogger::LogError(const std::string& procedureName, 
                                 int errorCode, 
                                 const std::string& errorMessage,
                                 const std::string& queryText,
                                 uint64_t aid,
                                 uint64_t cid)
{
    ErrorInfo error;
    error.serverName = m_serverName;
    error.procedureName = procedureName;
    error.errorCode = errorCode;
    error.errorMessage = errorMessage;
    error.queryText = queryText;
    error.aid = aid;
    error.cid = cid;
    error.timestamp = std::chrono::system_clock::now();
    
    LogError(error);
}

void DatabaseErrorLogger::LogErrorAsync(const ErrorInfo& error)
{
    if (!m_enabled || !m_logConnection)
        return;
    
    auto* workerPool = NSDataBaseManager::GetInstance()->GetWorkerPool();
    if (!workerPool)
        return;
    
    // 비동기로 에러 로깅 실행
    workerPool->PostWork([this, error]()
    {
        ExecuteErrorLogProcedure(error);
    });
}

bool DatabaseErrorLogger::ExecuteErrorLogProcedure(const ErrorInfo& error)
{
    if (!m_logConnection)
        return false;
    
    try
    {
        // spInsertDataBaseError 호출
        MySQLCommand command;
        
        // 타임스탬프를 문자열로 변환
        auto time_t = std::chrono::system_clock::to_time_t(error.timestamp);
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        
        // 파라미터 설정
        command.SetItem("ServerName", error.serverName);
        command.SetItem("ProcedureName", error.procedureName);
        command.SetItem("ErrorCode", static_cast<int32_t>(error.errorCode));
        command.SetItem("ErrorMessage", error.errorMessage);
        command.SetItem("QueryText", error.queryText);
        command.SetItem("Parameters", error.parameters);
        command.SetItem("ErrorTime", ss.str());
        command.SetItem("Aid", static_cast<int64_t>(error.aid));
        command.SetItem("Cid", static_cast<int64_t>(error.cid));
        
        // 에러 로깅은 실패해도 무시 (무한 루프 방지)
        m_logConnection->ExecuteProcedure("spInsertDataBaseError", &command);
        
        return true;
    }
    catch (...)
    {
        // 에러 로깅 자체가 실패한 경우 무시
        return false;
    }
}