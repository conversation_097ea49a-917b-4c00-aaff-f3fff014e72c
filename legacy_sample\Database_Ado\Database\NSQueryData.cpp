#include "stdafx.h"
#include "NSQueryData.h"

NSQueryData::NSQueryData()
{
	Reset();
}

NSQueryData::NSQueryData(const char* function, uint64_t line, std::shared_ptr<NSDBSession> session)
	: m_pSession(std::move(session))
	, m_Function(function)
	, m_Line(line)
{
}

NSQueryData::NSQueryData(const wchar_t* functionName, int line,
	const TYPE_QUERY_FUNC& queryFunc,
	const TYPE_RESULT_FUNC& resultFunc,
	std::shared_ptr<NSDBSession> session)
	: m_pQueryFunc(queryFunc)
	, m_pResultFunc(resultFunc)
	, m_pSession(std::move(session))
{
	NSPQueryData::SetAllocFuncName(functionName);
	NSPQueryData::SetAllocLine(line);
}

NSQueryData::~NSQueryData()
{
}

void NSQueryData::Reset()
{
	NSPQueryData::Reset();

	m_pQueryFunc = nullptr;
	m_pResultFunc = nullptr;
	m_pSession = nullptr;
}

void NSQueryData::RunProcQuery()
{
	if (m_pQueryFunc)
	{
		EErrorCode eErrorCode = m_pQueryFunc(shared_from_this());
		SetErrorCode(eErrorCode);
	}
}

void NSQueryData::RunResultQuery()
{
	if (m_pResultFunc)
	{
		m_pResultFunc(shared_from_this());
	}
}
