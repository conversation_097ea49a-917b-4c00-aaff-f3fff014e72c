#pragma once

#include <unordered_map>
#include <string>

#include "NPErrorCode.h"

constexpr uint32_t g_uMaxQueryRecordSetCount = 64;

class NSMySQLCommand;
class NSMySQLRecordset;
class NSDataSerializer;
class NSPQueryData
{
public:
	using MAP_RECORDSET = std::unordered_map<std::string, NSMySQLRecordset*>;
	using MAP_RETURNVALUE = std::unordered_map<std::string, int>;
	using MAP_COMMANDNAME = std::unordered_map<int, std::string>;

public:
	NSPQueryData();
	virtual ~NSPQueryData();
	void Reset();

public:
	NSDataSerializer& GetQueryData();
	NSMySQLRecordset* GetRecordSet();
	NSMySQLRecordset* GetRecordSet(int iIndex);
	NSMySQLRecordset* GetRecordSet(const char* strCommandName);
	virtual void RunProcQuery() = 0;
	virtual void RunResultQuery() = 0;

	void SetRecordSet(std::string strCommandName, NSMySQLRecordset* pcRecordSet);
	bool SetCommand(NSMySQLCommand* pcCommand);
	void SetReturnValue(int iIndex, std::string strCommandName, int iReturnValue);
	NSMySQLCommand* GetCommand(int iIndex = 0);
	const char* GetCommandName(int iIndex = 0);

	const int GetQuerySetCount() const { return m_iQuerySetCount; }

	int GetReturnValue();
	int GetReturnValue(int iIndex);
	int GetReturnValue(const char* strCommandName);

	void SetErrorCode(const EErrorCode eErrorCode);
	EErrorCode GetErrorCode() const;
	bool IsValid() const;

	void SetAllocFuncName(const wchar_t* pAllocFuncName) { m_pAllocFuncName = pAllocFuncName; }
	const wchar_t* GetAllocFuncName() { return m_pAllocFuncName; }

	void SetAllocLine(int iAllocLine) { m_iAllocLine = iAllocLine; }
	int GetAllocLine() const { return m_iAllocLine; }

	void SetElapsedTime(std::chrono::high_resolution_clock::duration elapsedTime);
	auto GetElapsedTime() const->std::chrono::high_resolution_clock::duration;

protected:
	NSDataSerializer* m_pcDataSerializer;
	NSMySQLCommand* m_pcCommand[g_uMaxQueryRecordSetCount];
	NSMySQLRecordset* m_pcRecordset[g_uMaxQueryRecordSetCount];
	int m_iReturnValue[g_uMaxQueryRecordSetCount];
	EErrorCode m_eErrorCode;

	MAP_RECORDSET m_mapRecordsetByName;
	MAP_RETURNVALUE m_mapReturnValueByName;
	MAP_COMMANDNAME m_mapCommandNameByIdx;

	int m_iQuerySetCount = 0;

	const wchar_t* m_pAllocFuncName;
	int m_iAllocLine;

	std::chrono::high_resolution_clock::duration m_ElapsedTime{};
};