#include "stdafx.h"
#include "NSClientSessionManager.h"
#include "Guid/NSGuidManager.h"
#include "NSGatePacketRouter.h"
#include "Module/NSGateLoginModule.h"
#include "NSClientHandler.h"
#include "Main/NSGateLogicThread.h"
#include "Network/NSCommunityServerHandler.h"
#include "../NSPublic/NSUtil/NSRedisUtil.h"
#include "NSXignCodeHandler.h"
#include "NPPacket.h"

bool NSClientSessionManager::AddSession(std::shared_ptr<NSClientSession>& session)
{
	const auto socketChannelId = session->GetSocketChannelId();
	auto find = m_SessionByChannelId.find(socketChannelId);
	if (find != m_SessionByChannelId.end())
		return false;

	if (!m_SessionByChannelId.emplace(socketChannelId, session).second)
		return false;

	session->SetSessionId(NSGuidManager::GetInstance()->GenerateGuid(NSGuidManager::Guid::GateSesion));
	int64_t sessionId = session->GetSessionId();
	if (!m_SessionBySessionId.emplace(sessionId, session).second)
	{
		assert(false);
		m_SessionByChannelId.erase(socketChannelId);
		return false;
	}

	return true;
}

auto NSClientSessionManager::GetSessionSize() const noexcept -> size_t
{
	return m_SessionByChannelId.size();
}

void NSClientSessionManager::CloseSession(int64_t clientChannelID)
{
	std::shared_ptr<NSClientSession> session = GetSessionByChannelId(clientChannelID);
	if (session == nullptr)
	{
		LOGE << std::format("CloseSession failed - Session not found for channel ID: {}", clientChannelID);
		return;
	}
	std::shared_ptr<NSCommunityServerSession> communitySession = NSCommunityServerHandler::GetInstance()->GetSession(session->GetSessionId());

	if (communitySession != nullptr)
	{
		NSPacketPlayerDisconnectFromGateNtf ntf;
		ntf.SetSessionUid(session->GetSessionId());
		communitySession->Send(ntf);
	}
	NSClientHandler::GetInstance()->OnClose(session->GetSocketChannelId(), "NSClientSessionManager::CloseSession");
}

void NSClientSessionManager::CleanZombie()
{
	constexpr size_t kZombieCleanThreshold = 1000;

	if (m_SessionByUnActiveChannelId.size() < kZombieCleanThreshold)
		return;

	const auto now = NSGateLogicThread::GetInstance()->GetCurrentTick();

	for (auto it = m_SessionByUnActiveChannelId.begin(); it != m_SessionByUnActiveChannelId.end(); )
	{
		if (it->second->IsZombie(now))
		{
			CloseSession(it->first);
			it = m_SessionByUnActiveChannelId.erase(it); // 안전한 삭제
		}
		else
		{
			++it;
		}
	}

	LOGI << std::format("Cleaned zombie sessions. Remaining unactive sessions: {}", m_SessionByUnActiveChannelId.size());
}

void NSClientSessionManager::CloseAllSession()
{
	for (auto& [_, session] : m_SessionByChannelId)
	{
		//커뮤서버로 디스커넥트 전송
		auto communitySession = NSCommunityServerHandler::GetInstance()->GetSession(session->GetSessionId());
		if (communitySession != nullptr)
		{
			NSPacketPlayerDisconnectFromGateNtf ntf;
			ntf.SetSessionUid(session->GetSessionId());
			communitySession->Send(ntf);
		}

		NSClientHandler::GetInstance()->OnClose(session->GetSocketChannelId(), "NSClientSessionManager::CloseAllSession");
	}
}

void NSClientSessionManager::CloseAllSession(int64_t worldServerChannelID)
{
	for (auto& [_, session] : m_SessionByChannelId)
	{
		if (session->GetWorldServerSessionIndex() == worldServerChannelID)
		{
			CloseSession(session->GetSocketChannelId());
		}
	}
}

bool NSClientSessionManager::UpdateSessionAID(std::shared_ptr<NSClientSession>& session)
{
	auto aid = session->GetAID();
	if (GetSessionByAID(aid) != nullptr)
	{
		LOGE << std::format("Duplicate AID [{}]", aid);
		return false;
	}

	if (session->GetOS() == "windows" &&
		session->IsPlatformLogin() && NSXignCodeHandler::GetInstance()->GetServer() != nullptr)
	{
		NSXignCodeHandler::GetInstance()->GetServer()->OnAccept(aid, NULL);
		NSXignCodeHandler::GetInstance()->GetServer()->SetUserInformationA(aid, 123, "UserID"); // ipv4		
	}

	return m_SessionByAid.emplace(aid, session).second;
}

void NSClientSessionManager::UpdateActiveSessionId(std::shared_ptr<NSClientSession>& session)
{
	const auto activeSessionId = session->GetActiveSessionID();

	// 비활성 세션 맵에서 제거
	m_SessionByUnActiveChannelId.erase(session->GetSocketChannelId());

	// 활성 세션 맵에 갱신
	m_SessionByActiveSessionId[activeSessionId] = session;
}

void NSClientSessionManager::UpdateUnActiveChannelId(std::shared_ptr<NSClientSession>& session)
{
	m_SessionByUnActiveChannelId[session->GetSocketChannelId()] = session;
}

auto NSClientSessionManager::RemoveSession(int64_t channelId) -> std::shared_ptr<NSClientSession>
{	
	auto findByChannelId = m_SessionByChannelId.find(channelId);
	if (findByChannelId == m_SessionByChannelId.end())
		return nullptr;

	auto session = findByChannelId->second;
	if (session->IsPlatformLogin() && NSXignCodeHandler::GetInstance()->GetServer() != nullptr)
	{
		NSXignCodeHandler::GetInstance()->GetServer()->OnDisconnect(session->GetAID());
	}
	m_SessionByChannelId.erase(findByChannelId);
	NSRedisUtil::RemoveSession(session->GetActiveSessionID(),session->GetRedisSessionToken());

	auto findByAid = m_SessionByAid.find(session->GetAID());
	if (findByAid != m_SessionByAid.end())
	{
		m_SessionByAid.erase(findByAid);		
	}	

	auto findBySessionId = m_SessionBySessionId.find(session->GetSessionId());
	if (findBySessionId != m_SessionBySessionId.end())
	{
		m_SessionBySessionId.erase(findBySessionId);
	}

	auto findByActiveSessionId = m_SessionByActiveSessionId.find(session->GetActiveSessionID());
	if (findByActiveSessionId != m_SessionByActiveSessionId.end())
	{
		m_SessionByActiveSessionId.erase(findByActiveSessionId);
	}

	return session;
}

auto NSClientSessionManager::GetSessionByChannelId(int64_t channelId) -> std::shared_ptr<NSClientSession>
{
	auto find = m_SessionByChannelId.find(channelId);
	if (find == m_SessionByChannelId.end())
		return nullptr;

	return find->second;
}

auto NSClientSessionManager::GetSessionBySessionId(int64_t sessionId) -> std::shared_ptr<NSClientSession>
{
	auto find = m_SessionBySessionId.find(sessionId);
	if (find == m_SessionBySessionId.end())
		return nullptr;

	return find->second;
}

auto NSClientSessionManager::GetSessionByAID(int64_t aid) -> std::shared_ptr<NSClientSession>
{
	auto find = m_SessionByAid.find(aid);
	if (find == m_SessionByAid.end())
		return nullptr;

	return find->second;
}

auto NSClientSessionManager::GetSessionByActiveSessionId(const std::string& pid) -> std::shared_ptr<NSClientSession>
{
	auto find = m_SessionByActiveSessionId.find(pid);
	if (find == m_SessionByActiveSessionId.end())
		return nullptr;
	
	return find->second;
}

void NSClientSessionManager::BroadcastMessage(std::string message, uint32_t duration)
{
	NPPacketNoticeContentsNtf noticeContentsNtf;
	noticeContentsNtf.SetContents(message.c_str());
	noticeContentsNtf.SetLocalize(ENpLib_LocalizeType::KOR);
	noticeContentsNtf.SetDuration(static_cast<int32_t>(duration));

	for (auto& [_, session] : m_SessionByChannelId)
	{
		session->Send(noticeContentsNtf);
	}
}