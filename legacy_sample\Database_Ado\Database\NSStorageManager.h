#pragma once

#include "NSSingleton.h"

class NSStorageManager : public TemplateSingleton<NSStorageManager>
{
private:
	static constexpr int32_t MAX_ATTEMPT_COUNT = 5;
	static constexpr uint64_t CONTAINER_TTL_MS = 86400000; // TODO: [Austin] 1day TTL for testing. 10 * 60 * 1000;

public:
	~NSStorageManager();

public:
	void Process();

	bool IsSelectPossible(int64_t cid) const;
	bool IsUpdatePossible(int64_t cid) const;
	bool IsRollbackPossible(int64_t cid, size_t modelCount) const;

	const int64_t GetNextSequence(int64_t cid);
	const int64_t GetUpdatedSequence(int64_t cid) const;
	const int64_t GetPendingSequence(int64_t cid) const;
	bool RollbackPendingSequence(int64_t cid, size_t modelCount);
	bool SetUpdatedSequence(int64_t cid, int64_t updatedSeq);
	void LoadUpdatedSequence(int64_t cid, int64_t updatedSeq);

	void SubtractPendingSequence(int64_t cid, size_t SubtractCount);

	const int32_t GetFailCount(int64_t cid) const;
	void AddFailCount(int64_t cid);

private:
	void RemoveData(int64_t cid);
	void Touch(int64_t cid);

private:
	uint64_t m_NextUpdateTick{ 0 };
	std::unordered_map<int64_t, uint64_t> m_ExpiredByCid;

	std::unordered_map<int64_t, int64_t> m_PendingSequenceByCid;
	std::unordered_map<int64_t, int64_t> m_UpdatedSequenceByCid;
	std::unordered_map<int64_t, int32_t> m_FailByCid;
};