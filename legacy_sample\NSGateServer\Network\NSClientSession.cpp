#include "stdafx.h"
#include <CNLCoreIO.h>

#include "NSClientSession.h"
#include "GameServer/NSWorldServerManager.h"
#include "NPPacket.h"
#include "NPModels.h"
#include "DataBase/StoredProcedure/Gate/SpLoginAccount.h"
#include "DataBase/NSDataBaseManager.h"
#include "GameServer/NSWorldServer.h"
#include "Main/NSGateLogicThread.h"
#include "NSTickManager/NSTickManager.h"
#include "Logger/NSLoggerBuidler.h"

NSClientSession::NSClientSession()
{
	m_CreateSessionTick = NSGateLogicThread::GetInstance()->GetCurrentTick();
}

NSClientSession::~NSClientSession()
{
	Reset();
}

bool NSClientSession::Send(const char* buffer, int size)
{
	if (m_SocketChannel == nullptr)
		return false;

	if (m_SocketChannelId == 0)
		return false;

	if (m_SocketChannelId != m_SocketChannel->GetChannelID())
	{
		LOGE << std::format("ChannelID MissMatch : {} => {}, W[{}] R[{}]",
			m_SocketChannelId, m_SocketChannel->GetChannelID(), m_SocketChannel->GetWriteBufferSize(), m_SocketChannel->GetReadBufferSize());
		return false;
	}

	if (m_SocketChannel->IsClosing() || m_SocketChannel->IsClose())
		return false;

	//	if (size > 16384)
	//	{
	//		auto test = reinterpret_cast<const NPPacket*>(buffer);
	//		LOGI << fmt::format("overrun packet type {0}, packet size {1}", test->GetType(), test->GetSize());
	//	}

	if (m_SendThenClose == true)
	{
		m_SocketChannel->Send_This_Then_CutOffIOPipe_Right_After(buffer, (cnl_packet_size_t)size, "Gate service logic set flag for 'send then close'");
	}
	else
	{
		m_SocketChannel->Send(buffer, (cnl_packet_size_t)size);
	}

	return true;
}

bool NSClientSession::WorldServerSend(char* packet, int32_t size)
{
	const auto worldServer = NSWorldServerManager::GetInstance()->GetWorldServer(m_WorldServerSid);
	if (worldServer == nullptr)
		return false;

	worldServer->Send(packet, size, m_WorldServerSessionIndex);
	return true;
}

void NSClientSession::Reset()
{
	m_SocketChannel = nullptr;
	m_PacketSequence = 0;
	m_SendThenClose = false;
	m_ActiveSessionID = "";
}

void NSClientSession::SetPlatformLogin(bool isPlaform)
{
	m_IsPlatformLogin = isPlaform;
}

bool NSClientSession::IsPlatformLogin() const
{
	return m_IsPlatformLogin;
}

void NSClientSession::SetSessionId(const int64 uid)
{
	m_SessionId = uid;
}

auto NSClientSession::GetSessionId() const -> int64_t
{
	return m_SessionId;
}

int64_t NSClientSession::GetSocketChannelId() const
{
	return m_SocketChannelId;
}

void NSClientSession::SetSocketChannel(const std::shared_ptr<CNLIOInterfaceAdapter>& channel)
{
	if (channel == nullptr)
		return;

	m_SocketChannel = channel;
	m_SocketChannelId = m_SocketChannel->GetChannelID();
}

const std::shared_ptr<CNLIOInterfaceAdapter>& NSClientSession::GetSocketChannel() const
{
	return m_SocketChannel;
}

void NSClientSession::BypassSend(char* packet)
{
	if (m_Closing)
		return;

	if (m_PacketSequence >= 0x3FFF)
		m_PacketSequence = 1;

	NPPacket* pcNPPacket = (NPPacket*)packet;
	pcNPPacket->SetSequence(++m_PacketSequence);

	m_SocketChannel->Send(packet, (cnl_packet_size_t)pcNPPacket->GetSize());
}


bool NSClientSession::IsZombie(uint64_t now)
{
	uint64_t compareTick = now - m_CreateSessionTick;
	if (compareTick > 2000)
		return true;

	return false;
}

void NSClientSession::Close()
{
	m_Closing = true;
}

bool NSClientSession::IsClosing() const
{
	if (m_SocketChannel == nullptr)
		return false;

	return m_SocketChannel->IsClosing();
}

bool NSClientSession::IsClosed() const
{
	if (m_SocketChannel == nullptr)
		return true;

	return m_SocketChannel->IsClose();
}

void NSClientSession::Disconnect()
{
	if (GetSocketChannel())
	{
		GetSocketChannel()->PostCloseTask();
	}
}

bool NSClientSession::SetSendThenClose(bool set)
{
	if (m_SocketChannel == nullptr)
		return false;

	m_SendThenClose = set;
	return true;

	//소켓 객체에 플래그 직접 세팅시
	//동시성으로인해 정확하게 동작하지 않을 가능성이 있어 재구현 되었습니다.
	//세션에 플래그 세팅후 Send 시도시
	//인터페이스에게 송신 후 종료를 요청합니다.
	//IO단에서 송신 후 종료를 보장합니다.

}

void NSClientSession::SendHeartbeat()
{
	m_HeartbeatTick = NSGateLogicThread::GetInstance()->GetCurrentTick();

	NPPacketHeartbeatAck packet;
	packet.SetServerTick(m_HeartbeatTick);
	Send(packet);
}

uint64_t NSClientSession::GetHeartbeatTick()
{
	return m_HeartbeatTick;
}

void NSClientSession::SetModuleID(int32_t moduleID)
{
	m_ModuleID = moduleID;
}

int32_t NSClientSession::GetModuleID()
{
	return m_ModuleID;
}

void NSClientSession::SetAID(int64_t aid)
{
	m_AID = aid;
}

int64_t NSClientSession::GetAID() const
{
	return m_AID;
}

auto NSClientSession::GetCID() const->int64_t
{
	assert(false && "ClientSession is not use CID");
	return m_CID;
}
void NSClientSession::SetActiveSessionID(const std::string& ActiveSessionID)
{
	m_ActiveSessionID = ActiveSessionID;
}
const std::string& NSClientSession::GetActiveSessionID() const
{
	return m_ActiveSessionID;
}

void NSClientSession::SetRedisSessionToken(const std::string& RedisSessionToken)
{
	m_RedisSessionToken = RedisSessionToken;
}
const std::string& NSClientSession::GetRedisSessionToken() const
{
	return m_RedisSessionToken;
}

void NSClientSession::SetPlatformAID(const std::string& platformAID)
{
	m_PlatformAID = platformAID;
}

void NSClientSession::SetCountry(const std::string& country)
{
	m_Country = country;
}

void NSClientSession::SetOS(const std::string& OS)
{
	m_OS = OS;
}

void NSClientSession::SetOSVersion(const std::string& OSVersion)
{
	m_OSVersion = OSVersion;
}

void NSClientSession::SetMarket(const std::string& market)
{
	m_Market = market;
}

void NSClientSession::SetDeviceInfo(const std::string& deviceInfo)
{
	m_DeviceInfo = deviceInfo;
}

void NSClientSession::SetUILanguage(const std::string& UILanguage)
{
	m_UILanguage = UILanguage;
}

void NSClientSession::SetPlatformType(const ENPPlatformType platformType)
{
	m_PlatformType = platformType;
}

void NSClientSession::SetPlatformID(const std::string& platformID)
{
	m_PlatformID = platformID;
}

void NSClientSession::SetIdentityProviderType(const ENPPlatformType identityProviderType)
{
	m_IdentityProviderType = identityProviderType;
}

void NSClientSession::SetIdentityProviderID(const std::string& identityProviderID)
{
	m_IdentityProviderID = identityProviderID;
}

auto NSClientSession::GetPlatformAID() const -> const std::string&
{
	return m_PlatformAID;
}

auto NSClientSession::GetCountry() const->const std::string&
{
	return m_Country;
}

auto NSClientSession::GetOS() const->const std::string&
{
	return m_OS;
}

auto NSClientSession::GetOSVersion() const->const std::string&
{
	return m_OSVersion;
}

auto NSClientSession::GetMarket() const->const std::string&
{
	return m_Market;
}

auto NSClientSession::GetDeviceInfo() const->const std::string&
{
	return m_DeviceInfo;
}

auto NSClientSession::GetUILanguage() const -> const std::string&
{
	return m_UILanguage;
}

auto NSClientSession::GetPlatformType() const -> const ENPPlatformType
{
	return m_PlatformType;
}

auto NSClientSession::GetPlatformID() const -> const std::string&
{
	return m_PlatformID;
}

auto NSClientSession::GetIdentityProviderType() const -> const ENPPlatformType
{
	return m_IdentityProviderType;
}

auto NSClientSession::GetIdentityProviderID() const -> const std::string&
{
	return m_IdentityProviderID;
}

bool NSClientSession::GetPayloadCheckSum(const char* buffer, int size)
{
	auto shift = 0;

	uint32_t checkSum = 0;
	for (auto i = 0; i < size; ++i)
	{
		checkSum += (static_cast<uint32_t>(buffer[i]) << shift);
		shift += 8;
		if (shift > 32)
		{
			shift = 0;
		}
	}

	return checkSum;
}

bool NSClientSession::CheckInputTime(uint16_t type, uint32_t checkSum, int intervalMS)
{
	auto currentTime = NSTickManager::GetInstance()->GetUnixTimeStamp();

	auto& payloadCheckSum = m_InputTimes[type];
	if (payloadCheckSum.first == checkSum && currentTime - payloadCheckSum.second < intervalMS)
	{
		payloadCheckSum.second = currentTime;
		return false;
	}

	payloadCheckSum.first = checkSum;
	payloadCheckSum.second = currentTime;
	return true;
}

void NSClientSession::BypassToWorld(char* packet, int32_t size)
{
	if (m_Closing)
		return;

	if (m_SocketChannel == nullptr)
		return;

	WorldServerSend(packet, size);
}

bool NSClientSession::SetWorldServer(std::shared_ptr<NSWorldServer> pcWorldServer)
{
	if (pcWorldServer == nullptr)
	{
		m_WorldServerSid = 0;
		m_WorldServerSessionIndex = 0;

		return false;
	}

	m_WorldServerSid = pcWorldServer->GetServerInfo().m_Sid;
	m_WorldServerSessionIndex = pcWorldServer->GetWorldServerSessionId(m_SessionId);

	return true;
}

auto NSClientSession::GetWorldServerSid() const -> uint64_t
{
	return m_WorldServerSid;
}

void NSClientSession::SendCommonNak(EErrorCode)
{
	//NPPacketCommonNak packet;
	//packet.SetResult(error);
	//Send(packet);
}

bool NSClientSession::IsDummyUser() const
{
	return m_DummyUserInfo.Active;
}

void NSClientSession::SetDummyUserInfo(NPDummyClientInfo dummyUserInfo)
{
	m_DummyUserInfo = dummyUserInfo;
}

auto NSClientSession::GetWorldServerSessionIndex() const->int64_t
{
	return m_WorldServerSessionIndex;
}

const std::string& NSClientSession::GetIpAddress()
{
	return m_SocketChannel->GetIpAddress();
}

void NSClientSession::GetActionLogHeader(NSKGLogger* outLogger, const std::string& category, const std::string& label, const std::string& action, const int32_t transactionKey) const
{
	outLogger->field("gameWorld", std::to_string(Service::GetInstance()->GetWid()).c_str());
	outLogger->field("channelId", std::to_string(static_cast<int64_t>(m_WorldServerSid)).c_str());
	outLogger->field("accountId", std::to_string(m_AID).c_str());
	outLogger->field("character1Id", std::to_string(m_CID).c_str());
	outLogger->field("playerId", m_PlatformAID.c_str());
	outLogger->field("country", m_Country.c_str());
	outLogger->field("os", m_OS.c_str());
	outLogger->field("osVersion", m_OSVersion.c_str());
	outLogger->field("market", m_Market.c_str());
	outLogger->field("deviceInfo", m_DeviceInfo.c_str());
	outLogger->field("uiLanguage", m_UILanguage.c_str());
	outLogger->field("ip", m_SocketChannel->GetIpAddress().c_str());

	outLogger->field("modTime", outLogger->GetPublishTime());
	outLogger->field("category", category.c_str());
	outLogger->field("labelNm", label.c_str());
	outLogger->field("label", label.c_str());
	outLogger->field("actionNm", action.c_str());
	outLogger->field("action", action.c_str());

	outLogger->field("log_tx", transactionKey);
}

void NSClientSession::SendSystemNtf(EErrorCode error)
{
	NPPacketSystemNtf packet;
	packet.SetResult(error);
	Send(packet);
}

void NSClientSession::SendSystemNtfThenClose(EErrorCode error)
{
	NPPacketSystemNtf packet;
	packet.SetResult(error);
	SendThenClose(packet);
}

void NSClientSession::SendCommonNtf(EErrorCode error)
{
	NPPacketCommonNtf packet;
	packet.SetResult(error);
	Send(packet);
}
