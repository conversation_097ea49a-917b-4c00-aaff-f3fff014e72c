#include "stdafx.h"
#include "NSWorldServerSessionManager.h"

bool NSWorldServerSessionManager::AddSession(std::shared_ptr<NSWorldServerSession>& session)
{
	auto socketChannelId = session->GetSocketChannelId();
	auto find = m_Session.find(socketChannelId);
	if (find != m_Session.end())
		return false;

	return m_Session.emplace(socketChannelId, session).second;
}

auto NSWorldServerSessionManager::GetSessionSize() const noexcept -> size_t
{
	return m_Session.size();
}

void NSWorldServerSessionManager::CloseAllSession()
{
	for (auto& [_, session] : m_Session)
	{
		session->Close();
	}
}

auto NSWorldServerSessionManager::RemoveSession(int64_t socketChannelId) -> std::shared_ptr<NSWorldServerSession>
{
	auto find = m_Session.find(socketChannelId);
	if (find == m_Session.end())
		return nullptr;

	auto session = find->second;
	m_Session.erase(find);

	return session;
}

auto NSWorldServerSessionManager::GetSession(int64_t socketChannelId) -> std::shared_ptr<NSWorldServerSession>
{
	auto find = m_Session.find(socketChannelId);
	if (find == m_Session.end())
		return nullptr;

	return find->second;
}

