#include <windows.h>

VS_VERSION_INFO VERSIONINFO
 FILEVERSION 0,1,5,55846
 PRODUCTVERSION 0,1,5,55846
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x40004L
 FILETYPE 0x1L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "041204b0"
        BEGIN
            VALUE "CompanyName", "ChonoStudio"
            VALUE "FileDescription", "GateServer Executable"
            VALUE "FileVersion", "0.1.383526.12298"
            VALUE "InternalName", "NSGateServer.exe"
            VALUE "LegalCopyright", "ChronoStudio 2024. All rights reserved."
            VALUE "OriginalFilename", "NSGateServer.exe"
            VALUE "ProductName", "GateServer"
            VALUE "ProductVersion", "0.1.383526.12298"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x412, 1200
    END
END
