#include "stdafx.h"
#include "NSGateGlobalSystem.h"

#include "Module/NSGateModule.h"
#include "Network/NSClientSession.h"
#include "Network/NSXignCodehandler.h"
#include "NSTickManager/NSTickManager.h"
#include "NSRandom.h"
#include "Main/NSGateLogicThread.h"
#include "NPModels.h"

NSGateGlobalSystem::NSGateGlobalSystem(NSGateModule* gateModule)
	: NSGateSystemBase(gateModule)
{
}

bool NSGateGlobalSystem::Init()
{
	INSERT_PROCESSOR(NSGateGlobalSystem::PacketServerLocalTimeReq, NPPacketServerLocalTimeReq);
	INSERT_PROCESSOR(NSGateGlobalSystem::PacketXignCodeDataReq, NPPacketXignCodeDataReq);

	return true;
}

bool NSGateGlobalSystem::Reset()
{
	return false;
}

void NSGateGlobalSystem::PacketServerLocalTimeReq(std::shared_ptr<NSClientSession>& session, NPPacketServerLocalTimeReq*)
{
	if (!session)
		return;

	NPPacketServerLocalTimeAck ack;
	ack.SetUTCTime(NSTickManager::GetInstance()->GetUnixTimeStamp());
	ack.SetTimeZoneOffsetMin(NSTickManager::GetInstance()->GetLocalTime().GetTimeZoneOffset().count());
	session->Send(ack);
}

void NSGateGlobalSystem::PacketXignCodeDataReq(std::shared_ptr<NSClientSession>& session, NPPacketXignCodeDataReq* packet)
{
	if (!session)
		return;
	
	if (NSXignCodeHandler::GetInstance()->GetServer() != nullptr)
	{
		NSXignCodeHandler::GetInstance()->GetServer()->OnReceive(session->GetAID(), packet->GetData(), XIGNCODE_PACKET_SIZE);
	}	
}