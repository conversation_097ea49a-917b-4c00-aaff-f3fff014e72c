#pragma once
#include <string>
#include <chrono>
#include <memory>

// Forward declarations
class NSMySQLConnection;
class MySQLCommand;

// 데이터베이스 에러 로깅 시스템
class DatabaseErrorLogger
{
public:
    struct ErrorInfo
    {
        std::string serverName;
        std::string procedureName;
        std::string errorMessage;
        int errorCode = 0;
        std::string queryText;
        std::string parameters;
        std::chrono::system_clock::time_point timestamp;
        uint64_t aid = 0;  // Account ID
        uint64_t cid = 0;  // Character ID
    };

    static DatabaseErrorLogger& GetInstance()
    {
        static DatabaseErrorLogger instance;
        return instance;
    }

    // 에러 로깅 활성화/비활성화
    void SetEnabled(bool enabled) { m_enabled = enabled; }
    bool IsEnabled() const { return m_enabled; }

    // 에러 로깅
    void LogError(const ErrorInfo& error);
    void LogError(const std::string& procedureName, 
                  int errorCode, 
                  const std::string& errorMessage,
                  const std::string& queryText = "",
                  uint64_t aid = 0,
                  uint64_t cid = 0);

    // 비동기 에러 로깅
    void LogErrorAsync(const ErrorInfo& error);

    // 로그 DB 연결 설정
    void SetLogConnection(NSMySQLConnection* connection) { m_logConnection = connection; }

private:
    DatabaseErrorLogger() = default;
    ~DatabaseErrorLogger() = default;
    
    // spInsertDataBaseError 실행
    bool ExecuteErrorLogProcedure(const ErrorInfo& error);

private:
    bool m_enabled = true;
    NSMySQLConnection* m_logConnection = nullptr;
    std::string m_serverName = "GameServer";
};

// 매크로 헬퍼
#define LOG_DB_ERROR(proc, code, msg) \
    DatabaseErrorLogger::GetInstance().LogError(proc, code, msg)

#define LOG_DB_ERROR_WITH_CONTEXT(proc, code, msg, query, aid, cid) \
    DatabaseErrorLogger::GetInstance().LogError(proc, code, msg, query, aid, cid)