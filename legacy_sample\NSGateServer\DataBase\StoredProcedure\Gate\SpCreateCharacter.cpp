#include "stdafx.h"
#include "SpCreateCharacter.h"

#include "ADO/NSAdoCommand.h"

#include "GameServer/NSWorldServerManager.h"
#include "NSRandom.h"

#include "Guid/NSGuidManager.h"

SpCreateCharacter::Sp<PERSON>reateCharacter(const int64_t aid, const int64_t cid, const int32_t wid, const char* name, const ENpLib_CharacterClassType classType, const uint8_t gender, const int32_t height, const char* customizePayload)
{
	Input.Aid = aid;
	Input.Cid = cid;
	Input.Wid = wid;
	NSUtil::SafeStrCpy(Input.Name, name);
	Input.ClassType = classType;
	Input.Gender = gender;
	Input.Height = height;

	NSUtil::SafeStrCpy(Input.CustomizePayload, customizePayload);

	const NSCharacterDefaultInfo& characterDefaultInfo = NSWorldServerManager::GetInstance()->GetCharacterDefaultInfo(classType);

	Input.DefaultChronotectorNodeId = characterDefaultInfo.DefaultChronotectorNodeId;
	Input.NodePoint = characterDefaultInfo.NodePoint;
	characterDefaultInfo.DefaultCharacterGoods.copy(Input.DefaultCharacterGoods, characterDefaultInfo.DefaultCharacterGoods.length());
	characterDefaultInfo.DefaultClasses.copy(Input.DefaultClasses, characterDefaultInfo.DefaultClasses.length());
	characterDefaultInfo.DefaultEquipmentJson.copy(Input.DefaultEquipmentJson, characterDefaultInfo.DefaultEquipmentJson.length());
	characterDefaultInfo.DefaultMainQuestJson.copy(Input.DefaultMainQuestJson, characterDefaultInfo.DefaultMainQuestJson.length());
	characterDefaultInfo.DefaultWeaponMastery.copy(Input.DefaultWeaponMastery, characterDefaultInfo.DefaultWeaponMastery.length());
	characterDefaultInfo.DefaultChronotectorActionJson.copy(Input.DefaultChronotectorActionJson, characterDefaultInfo.DefaultChronotectorActionJson.length());

	rapidjson::Document jsonDoc;
	jsonDoc.Parse(characterDefaultInfo.RewardPayload.c_str());

	if (!jsonDoc.IsNull() && jsonDoc.HasMember("IN") && jsonDoc["IN"].IsArray())
	{
		auto jsonArr = jsonDoc["IN"].GetArray();
		for (auto& jsonObj : jsonArr)
		{
			if (jsonObj.HasMember("U"))
			{
				jsonObj["U"].SetInt64(NSGuidManager::GetInstance()->GenerateGuid(NSGuidManager::Guid::Item));
			}
		}
	}

	rapidjson::StringBuffer result;
	rapidjson::Writer<rapidjson::StringBuffer> writer(result);
	jsonDoc.Accept(writer);

	strcpy_s(Input.RewardPayload, result.GetString());
}

EErrorCode SpCreateCharacter::MakeQuery(NSAdoCommand* command)
{
	command->SetItem("AID", Input.Aid);
	command->SetItem("CID", Input.Cid);
	command->SetItem("WID", Input.Wid);
	if (!command->SetItem("Name", Input.Name))
		return EErrorCode::DBArgumentError;
	command->SetItem("Gender", Input.Gender);
	command->SetItem("Height", Input.Height);

	command->SetItem("AddChronotectorNode", Input.DefaultChronotectorNodeId);
	command->SetItem("AddNodePoint", Input.NodePoint);
	if (!command->SetItem("AddCharacterGoodsJson", Input.DefaultCharacterGoods))
		return EErrorCode::DBArgumentError;	
	if (!command->SetItem("AddCharacterClassesJson", Input.DefaultClasses))
		return EErrorCode::DBArgumentError;
	if (!command->SetItem("AddDefaultItemJson", Input.RewardPayload))
		return EErrorCode::DBArgumentError;
	if (!command->SetItem("AddDefaultEquipmentJson", Input.DefaultEquipmentJson))
		return EErrorCode::DBArgumentError;
	if (!command->SetItem("AddDefaultMainQuestJson", Input.DefaultMainQuestJson))
		return EErrorCode::DBArgumentError;
	if (!command->SetItem("AddDefaultWeaponMasteryJson", Input.DefaultWeaponMastery))
		return EErrorCode::DBArgumentError;
	if (!command->SetItem("CustomizePayload", Input.CustomizePayload))
		return EErrorCode::DBArgumentError;
	if (!command->SetItem("AddChronotectorActionJson", Input.DefaultChronotectorActionJson))
		return EErrorCode::DBArgumentError;

	return EErrorCode::None;
}

EErrorCode SpCreateCharacter::HandleReturnValue(const int32_t returnValue)
{
	switch (returnValue)
	{
	case EProcReturnValue::CharacterError:
		return EErrorCode::CharacterError;
	default:
		return static_cast<EErrorCode>(returnValue);
	}
}