#include "stdafx.h"
#include "NPPacket.h"
#include "NPModels.h"
#include "NSXignCodeHandler.h"
#include "NSClientSessionManager.h"

NSXignCodeHandler::NSXignCodeHandler()
	: m_XignCodeServer(nullptr)
{
}

NSXignCodeHandler::~NSXignCodeHandler()
{	
}

void NSXignCodeHandler::InitializeXignCode()
{
	constexpr const char* ConfigDir{ "xigncode" };
	fs::path workDir = fs::current_path();
	fs::path modulePath = workDir / ConfigDir;
	CreateXigncodeServer2 CXProc = LoadHelperDll2(modulePath.string().c_str());
	if (!CXProc)
	{
		LOGE << std::format("Initialize XignCode Fail");
		//ULONG ErrorCode = GetLastError();		
		return;
	}

	const bool enableKick = NSConfigManager::GetConfigs().Common.AntiCheat.EnableKick;
	XigncodeDisconnectCallback2A disconnectCallback = enableKick ? DisconnectProc : nullptr;
	if (!CXProc(&m_XignCodeServer, SendProc, disconnectCallback))
	{
		LOGE << std::format("Initialize XignCode Fail");
		//ULONG ErrorCode = GetLastError();		
		return;
	}

	if (!m_XignCodeServer->OnBegin(XIGNCODE_PACKET_SIZE))
	{
		LOGE << std::format("Initialize XignCode Fail");
		//ULONG ErrorCode = GetLastError();		
		return;
	}
}

void NSXignCodeHandler::ReleaseXignCode()
{
	if (m_XignCodeServer)
	{
		m_XignCodeServer->OnEnd();
		m_XignCodeServer->Release();		
	}
}

xbool XCALL NSXignCodeHandler::SendProc(xuint64 uid, xpvoid/* context*/, xpcch data, xulong datasize)
{	
	const std::shared_ptr<NSClientSession> clientSession = NSClientSessionManager::GetInstance()->GetSessionByAID(uid);
	if (clientSession == nullptr)
	{
		if (NSXignCodeHandler::GetInstance()->GetServer() != nullptr)
		{
			NSXignCodeHandler::GetInstance()->GetServer()->OnDisconnect(uid);
			return false;
		}
	}	
	NPPacketXignCodeDataNtf clientAck;
	clientAck.SetDataSize(datasize);
	clientAck.SetData(data, datasize);
	clientSession->Send(clientAck);
	return true;
}

void XCALL NSXignCodeHandler::DisconnectProc(xuint64 uid, xpvoid/* context*/, xint/* code*/, xctstr/* report*/)
{

	if (NSXignCodeHandler::GetInstance()->GetServer() != nullptr)
	{
		NSXignCodeHandler::GetInstance()->GetServer()->OnDisconnect(uid);

		const std::shared_ptr<NSClientSession> clientSession = NSClientSessionManager::GetInstance()->GetSessionByAID(uid);
		if (clientSession != nullptr)
		{
			clientSession->SendSystemNtfThenClose(EErrorCode::UserKick);
			LOGE << std::format("XignCode kick AID:{}", uid);
		}
	}
}
