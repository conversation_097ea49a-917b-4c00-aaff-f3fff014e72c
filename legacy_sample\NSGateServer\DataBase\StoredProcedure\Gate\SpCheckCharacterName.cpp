#include "stdafx.h"
#include "SpCheckCharacterName.h"

#include "ADO/NSAdoCommand.h"

SpCheckCharacterName::SpCheckCharacterName(const char* name, const int32_t wid)
{
	NSUtil::SafeStrCpy(Input.Name, name);
	Input.Wid = wid;
}

EErrorCode SpCheckCharacterName::MakeQuery(NSAdoCommand* command)
{
	if (!command->SetItem("Name", Input.Name))
		return EErrorCode::DBArgumentError;

	command->SetItem("WID", Input.Wid);
	return EErrorCode::None;
}

EErrorCode SpCheckCharacterName::MakeOutput(NSAdoCommand* command)
{
	command->GetItem("Result", Output.Result);
	return EErrorCode::None;
}