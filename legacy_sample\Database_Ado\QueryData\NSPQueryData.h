#pragma once

#include <unordered_map>
#include <string>

#include "NPErrorCode.h"

constexpr uint32_t g_uMaxQueryRecordSetCount = 64;

class NSAdoCommand;
class NSAdoRecordset;
class NSDataSerializer;
class NSPQueryData
{
public:
	using MAP_RECORDSET = std::unordered_map<std::string, NSAdoRecordset*>;
	using MAP_RETURNVALUE = std::unordered_map<std::string, int>;
	using MAP_COMMANDNAME = std::unordered_map<int, std::string>;

public:
	NSPQueryData();
	virtual ~NSPQueryData();
	void Reset();

public:
	NSDataSerializer& GetQueryData();
	NSAdoRecordset* GetAdoRecordSet();
	NSAdoRecordset* GetAdoRecordSet(int iIndex);
	NSAdoRecordset* GetAdoRecordSet(const char* strCommandName);
	virtual void RunProcQuery() = 0;
	virtual void RunResultQuery() = 0;

	void SetAdoRecordSet(std::string strCommandName, NSAdoRecordset* pcRecordSet);
	bool SetAdoCommand(NSAdoCommand* pcAdoCommand);
	void SetReturnValue(int iIndex, std::string strCommandName, int iReturnValue);
	NSAdoCommand* GetAdoCommand(int iIndex = 0);
	const char* GetCommandName(int iIndex = 0);

	const int GetQuerySetCount() const { return m_iQuerySetCount; }

	int GetReturnValue();
	int GetReturnValue(int iIndex);
	int GetReturnValue(const char* strCommandName);

	void SetErrorCode(const EErrorCode eErrorCode);
	EErrorCode GetErrorCode() const;
	bool IsValid() const;

	void SetAllocFuncName(const wchar_t* pAllocFuncName) { m_pAllocFuncName = pAllocFuncName; }
	const wchar_t* GetAllocFuncName() { return m_pAllocFuncName; }

	void SetAllocLine(int iAllocLine) { m_iAllocLine = iAllocLine; }
	int GetAllocLine() const { return m_iAllocLine; }

	void SetElapsedTime(std::chrono::high_resolution_clock::duration elapsedTime);
	auto GetElapsedTime() const->std::chrono::high_resolution_clock::duration;

protected:
	NSDataSerializer* m_pcDataSerializer;
	NSAdoCommand* m_pcAdoCommand[g_uMaxQueryRecordSetCount];
	NSAdoRecordset* m_pcAdoRecordset[g_uMaxQueryRecordSetCount];
	int m_iReturnValue[g_uMaxQueryRecordSetCount];
	EErrorCode m_eErrorCode;

	MAP_RECORDSET m_mapRecordsetByName;
	MAP_RETURNVALUE m_mapReturnValueByName;
	MAP_COMMANDNAME m_mapCommandNameByIdx;

	int m_iQuerySetCount = 0;

	const wchar_t* m_pAllocFuncName;
	int m_iAllocLine;

	std::chrono::high_resolution_clock::duration m_ElapsedTime{};
};
