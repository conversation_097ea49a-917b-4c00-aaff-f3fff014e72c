#include "stdafx.h"
#include "NSExecutor.h"

#include <cassert>
#include <shared_mutex>
#include "Detail/ThreadPool.h"

namespace Promise::Detail
{
	auto GetStorage() ->std::pair<std::shared_mutex, std::unordered_map<std::thread::id, NSExecutor*>>&
	{
		static std::pair<std::shared_mutex, std::unordered_map<std::thread::id, NSExecutor*>> instance;
		return instance;
	}
}

namespace Promise
{
	NSExecutor::~NSExecutor()
	{
	}

	void SetCurrentExecutor(std::thread::id tid, NSExecutor* executor)
	{
		auto& [mutex, executors] = Detail::GetStorage();
		std::unique_lock lock(mutex);

		if (executor)
		{
			[[maybe_unused]] bool result = executors.try_emplace(tid, executor).second;
			assert(result);
		}
		else
		{
			[[maybe_unused]] bool result = executors.erase(tid);
			assert(result);
		}
	}

	auto CurrentExecutor() -> NSExecutor&
	{
		std::thread::id id = std::this_thread::get_id();
		{
			auto& [mutex, executors] = Detail::GetStorage();
			std::shared_lock lock(mutex);

			auto iter = executors.find(id);
			if (iter != executors.end())
			{
				return *iter->second;
			}
		}

		return ThreadPool();
	}

	auto ThreadPool() -> NSExecutor&
	{
		return Detail::ThreadPool::GetInstance();
	}
}
