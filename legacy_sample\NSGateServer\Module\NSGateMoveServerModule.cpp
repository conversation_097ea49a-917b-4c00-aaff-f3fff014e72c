#include "stdafx.h"
#include "NSGateMoveServerModule.h"

#include "Network/NSClientSession.h"
#include "Network/NSClientSessionManager.h"
#include "Network/NSCommunityServerHandler.h"

#include "Data/NSCharacterTemplate.h"
#include "Data/NSWorldTriggerTemplate.h"
#include "Data/NSSummonTemplate.h"
#include "Data/NSCampTemplate.h"
#include "Data/NSGameConfigTemplate.h"
#include "Packet/NSPacketData.h"
#include "NPModels.h"
#include "NSRandom.h"
#include "NSModels/NSModels.h"

NSGateMoveServerModule::NSGateMoveServerModule()
{

}

NSGateMoveServerModule::~NSGateMoveServerModule()
{
	NSGateMoveServerModule::Reset();
}

void NSGateMoveServerModule::Init()
{
	NSGateModule::Init();
}

void NSGateMoveServerModule::Reset()
{
	NSGateModule::Reset();
}

void NSGateMoveServerModule::Process(uint64_t nowTick, uint64_t elapsedTickCount)
{
	NSGateModule::Process(nowTick, elapsedTickCount);
}

bool NSGateMoveServerModule::OnAddSession(std::shared_ptr<NSClientSession> session)
{
	// 여기는 MoveSession으로만 올 수 있다
	LOGE << std::format("[Server] MapModule - Invalid AddSession[SocketChannelId:{}]", session->GetSessionId());
	return false;
}

bool NSGateMoveServerModule::OnMoveSession(std::shared_ptr<NSClientSession>)
{
	return true;
}

bool NSGateMoveServerModule::OnRemoveSession(std::shared_ptr<NSClientSession>)
{
	return true;
}

bool NSGateMoveServerModule::OnDisconnect(std::shared_ptr<NSClientSession> session)
{
	auto communitySession = NSCommunityServerHandler::GetInstance()->GetSession(session->GetSessionId());
	if (communitySession != nullptr)
	{
		NSPacketLogoutSyncToCommunityServerNtf ntf;
		ntf.SetAID(session->GetAID());
		communitySession->Send(ntf);
	}

	return true;
}

void NSGateMoveServerModule::HandlePacket(NSPacketData* )
{
	//const auto socketChannelId = packetData->GetSocketChannelID();
	//const auto packet = reinterpret_cast<NPPacket*>(packetData->GetBuffer());

	//if (packet == nullptr)
	//{
	//	LOGW << std::format("HandlePacket handles null packet!");
	//	return;
	//}

	//auto session = GetSessionByChannelId(socketChannelId);
	//if (session == nullptr)
	//	return;

	//if (session->IsClosing())
	//	return;

	// heartbeat 핸들러 없이 바로 처리해준다.
	//switch (packet->GetType())
	//{
	//case EPacketType_1000::ePacketType_HeartbeatReq:
	//{
	//	session->SendHeartbeat();
	//	return;
	//}
	//break;
	//}

	//std::string packetName = NPPacketType_1000_GetPacketName(packet->GetType());
	//LOGD << std::format("Recv MoveServer Packet  [Name : {}], [Code : {}], [Size : {}/Byte]",
	//	packetName, packet->GetType(), packet->GetSize());

	//const auto packetType = packet->GetType();
	//const auto it = m_PacketProcessor.find(packetType);
	//if (it == m_PacketProcessor.end())
	//	return;

	//if (packetType >= g_uMaxPacketTypeEnum)
	//	return;

	//session->OnRecv(packet);
	//const PROCESSHANDLE& processor = it->second;
	//processor(session, reinterpret_cast<char*>(packet));
}

