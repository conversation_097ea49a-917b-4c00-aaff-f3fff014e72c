#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpGetEquipment : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spGetEquipment";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);

public:
	struct Input
	{
		int64_t Cid = 0;
        uint8_t PresetIdx = 0;
	} Input;

	struct Record
	{
		uint8_t Slot = 0;
		int64_t ItemUid = 0;
		int32_t ItemId = 0;
	};

	SpGetEquipment() = default;
	SpGetEquipment(const int64_t Cid, const uint8_t PresetIdx = 0);
};
