#pragma once
#include <thread>
#include <functional>

namespace Promise
{
	class NSExecutor
	{
	public:
		NSExecutor(const NSExecutor&) = delete;
		NSExecutor(NSExecutor&&) noexcept = delete;
		NSExecutor& operator=(const NSExecutor&) = delete;
		NSExecutor& operator=(NSExecutor&&) noexcept = delete;

		NSExecutor() = default;
		virtual ~NSExecutor();

		virtual bool RunningInThisThread() const = 0;
		virtual void Post(const std::function<void()>& function) = 0;
	};

	void SetCurrentExecutor(std::thread::id tid, NSExecutor* executor);

	auto CurrentExecutor() -> NSExecutor&;
	auto ThreadPool() -> NSExecutor&;
}