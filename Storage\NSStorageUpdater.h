#pragma once

#include "../NSDefine.h"
#include "NSStorageUpdateContainer.h"
#include <memory>
#include <vector>
#include <set>
#include <functional>
#include <source_location>
#include <rapidjson/writer.h>
#include <rapidjson/stringbuffer.h>

// Forward declarations
class NSStorageModel;
class NSQueryData;
class NSDBSession;
class NSDataSerializer;

enum class EEscrowType
{
    None = 0,
    // 필요에 따라 추가
};

struct NSEscrowData
{
    int64_t SenderCid{ 0 };
    int64_t ReceiverCid{ 0 };
    EEscrowType EscrowType{ EEscrowType::None };
    int32_t EscrowId{ 0 };
    int32_t EscrowQuantity{ 0 };
    int64_t EscrowUid{ 0 };
};

class NSStorageUpdater
{
public:
    static constexpr int64_t ESCROW_TO_SYSTEM = -1;
    NSStorageUpdater(std::shared_ptr<NSDBSession> session, int64_t aid, int64_t cid, std::source_location location = std::source_location::current());
    NSStorageUpdater() = delete;
    ~NSStorageUpdater();

public:
    static void Disconnect(std::shared_ptr<NSDBSession> session);
    static void MakeEscrowPayload(rapidjson::Writer<rapidjson::StringBuffer>& writer, const NSEscrowData& escrowData);

public:
    std::vector<std::tuple<int64_t, NSStorageModel*>> GetModel();
    std::vector<TYPE_RESULT_MODEL_FUNC> GetResultFunc();
    void AddModelToUpdate(NSStorageModel* componentModel, std::source_location location = std::source_location::current());
    void AddResultFunc(const TYPE_RESULT_MODEL_FUNC& resultFunc);
    void Rollback(std::source_location location = std::source_location::current());
    void Commit(const TYPE_RESULT_MODEL_FUNC& resultFunc = nullptr, std::source_location location = std::source_location::current());
    void CommitForce(const TYPE_RESULT_MODEL_FUNC& resultFunc = nullptr, std::source_location location = std::source_location::current());
    void CommitWithEscrowDeposit(const int64_t escrowReceiverCid, const TYPE_RESULT_MODEL_FUNC& resultFunc = nullptr, std::source_location location = std::source_location::current());
    void CommitWithEscrowWithdraw(int64_t escrowTransactionId, const TYPE_RESULT_MODEL_FUNC& resultFunc = nullptr, std::source_location location = std::source_location::current());
    void CommitWithEscrowReclaim(const int64_t escrowTransactionId, const TYPE_RESULT_MODEL_FUNC& resultFunc = nullptr, std::source_location location = std::source_location::current());
#ifdef _DEBUG
    void SetCommitCalled();
#endif

    template<typename SpProcedure>
    void CommitWithCustomProcedure(NSDataSerializer& dataSerializer, const TYPE_RESULT_MODEL_FUNC& resultFunc = nullptr, std::source_location location = std::source_location::current());

protected:
    static auto ProcessSequence(const std::shared_ptr<NSStorageUpdateContainer> container) -> EErrorCode;

private:
    void QueryFunc(EStorageUpdateMethod method, const TYPE_RESULT_MODEL_FUNC& resultFunc, std::source_location location, bool isForce, int64_t escrowTransactionId);
    static bool PostQuery(const std::shared_ptr<NSStorageUpdateContainer> container);
    static bool PostQueryDelay(int delayMS, const std::shared_ptr<NSStorageUpdateContainer> container);
    static auto UpdateFunc(const std::shared_ptr<NSQueryData> queryData, const std::shared_ptr<NSStorageUpdateContainer> container) -> EErrorCode;
    static auto ResultFunc(const std::shared_ptr<NSQueryData> queryData, const std::shared_ptr<NSStorageUpdateContainer> container) -> EErrorCode;

    template<typename SpProcedure>
    static bool PostCustomQuery(NSDataSerializer& dataSerializer, const std::shared_ptr<NSStorageUpdateContainer> container);

    static void DisconnectSession(std::shared_ptr<NSDBSession> session, EErrorCode error);

private:
    int32_t m_Wid{ 0 };
    int64_t m_Aid{ 0 };
    int64_t m_Cid{ 0 };
    std::shared_ptr<NSDBSession> m_Session;
    int64_t m_EscrowReceiverCid{ 0 };
    std::vector<TYPE_RESULT_MODEL_FUNC> m_ResultFuncs;

    std::set<int32_t> m_ModelIds;
    std::vector<std::tuple<int64_t, NSStorageModel*>> m_Models;
#ifdef _DEBUG
    bool m_CommitCalled{ false };
#endif
};

template<typename SpProcedure>
void NSStorageUpdater::CommitWithCustomProcedure(NSDataSerializer& dataSerializer, const TYPE_RESULT_MODEL_FUNC& resultFunc, [[maybe_unused]] std::source_location location)
{
#ifdef _DEBUG
    assert(!m_CommitCalled);
    LOG_DEBUG("NSStorageUpdater::CommitWithCustomProcedure() has started from {}:{}", location.file_name(), location.line());
    m_CommitCalled = true;
#endif

    if (m_Models.size() != 1)
    {
        LOG_ERROR("NSStorageUpdater::CommitWithCustomProcedure() only allows 1 model with escrow");
        return;
    }

    if (resultFunc != nullptr)
    {
        AddResultFunc(resultFunc);
    }

    size_t currentIdx = 0;
    size_t modelCount = m_Models.size();
    for (auto& [seq, model] : m_Models)
    {
        ++currentIdx;
        if (model == nullptr)
        {
            LOG_WARN("NSStorageUpdater::CommitWithCustomProcedure() found a nullptr model");
            continue;
        }

        auto container = std::make_shared<NSStorageUpdateContainer>();
        container->Wid = m_Wid;
        container->Aid = m_Aid;
        container->Cid = m_Cid;
        container->Session = m_Session;
        container->Seq = seq;
        container->ModelId = model->GetModelId();
        container->Method = EStorageUpdateMethod::CustomProcedure;

        model->SerializeAffected(container->Payloads);
        if (container->Payloads.empty())
        {
            LOG_DEBUG("NSStorageUpdater::CommitWithCustomProcedure() Model #{} has empty payload", model->GetModelId());
            continue;
        }

        if (currentIdx == modelCount)
        {
            container->Callbacks.swap(m_ResultFuncs);
        }

        PostCustomQuery<SpProcedure>(dataSerializer, container);
    }
}

template<typename SpProcedure>
bool NSStorageUpdater::PostCustomQuery(NSDataSerializer& dataSerializer, const std::shared_ptr<NSStorageUpdateContainer> container)
{
    if (!NSStorageManager::GetInstance()->IsUpdatePossible(container->Cid))
        return PostQueryDelay(1000, container);

#ifdef _DEBUG
    LOG_DEBUG("NSStorageUpdater::PostQuery() Model #{} seq {}", container->ModelId, container->Seq);
    LOG_DEBUG("NSStorageUpdater::PostQuery() Model #{} has {} payloads.", container->ModelId, container->Payloads.size());
#endif

    // 비동기로 StorageUpdateQueryWithCustomProcedure 실행
    NSDataBaseManager::GetInstance()->StorageUpdateQueryWithCustomProcedure<SpProcedure>(container, dataSerializer,
        std::bind(&NSStorageUpdater::ResultFunc, std::placeholders::_1, std::placeholders::_2),
        container->Session)
    .Then([container](std::shared_ptr<NSQueryData> queryData) {
        // 성공 처리는 ResultFunc에서 이미 완료됨
    })
    .Catch([container](std::exception_ptr e) {
        try {
            std::rethrow_exception(e);
        } catch (const std::exception& ex) {
            LOG_ERROR("StorageUpdateQueryWithCustomProcedure failed for Model #{} seq {}: {}", 
                container->ModelId, container->Seq, ex.what());
            DisconnectSession(container->Session, EErrorCode::DBError);
        }
    });

    return true;
}