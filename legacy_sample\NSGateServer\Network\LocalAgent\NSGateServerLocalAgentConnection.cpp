#include "stdafx.h"
#include <CNLCoreIO.h>

#include "NSGateServerLocalAgentConnection.h"
#include "DataBase/NSDataBaseManager.h"
#include "../NSPublic/ServerMain/NSServerMain.h"
#include "../NSPublic/LocalAgent/LocalAgentDS.h"

using ThisPeerType = NSGateServerLocalAgentConnection;
const std::unordered_map<std::string, void (ThisPeerType::*)(char*, cnl_packet_size_t)> NSGateServerLocalAgentConnection::PacketHandlers = {
	{ "PACKET_FREE_FORMAT_JSON", &ThisPeerType::OnRecvJson },
};

const std::unordered_map<std::string, void (ThisPeerType::*)(json11::Json&)> NSGateServerLocalAgentConnection::JsonPayloadHandlers = {
	{ "JSON_PAYLOAD_TYPE_TERMINATE_PROCESS",&ThisPeerType::OnRecvJsonTerminateProcess },
};

struct FREE_FORMAT_JSON
{
	cnl_packet_size_t Size{ 0 }; //size of packet
	inline static const std::string Type = "PACKET_FREE_FORMAT_JSON"; //type of packet
	std::string JsonDocument;
};

inline size_t CNLGenSzdSize(const FREE_FORMAT_JSON& e)
{
	size_t ret = CNLGenSzdSize(e.Size, e.Type, e.JsonDocument);
	return ret;
}

inline CNLOutStream& operator<<(CNLOutStream& lhs, const FREE_FORMAT_JSON& rhs)
{
	size_t streamLen = CNLGenSzdSize(rhs);
	if (lhs.EvaluateOverflow(streamLen) == true)
		return lhs;

	lhs << (cnl_packet_size_t)streamLen << rhs.Type << rhs.JsonDocument;
	return lhs;
}

void NSGateServerLocalAgentConnection::Update(unsigned int elapsedTime)
{
	CNLServerInfo::GetInstance()->SetUpdateElapsedTime(elapsedTime);

	//서버 상태를 로컬 에이전트로 전송합니다.
	if (UpdateReportServerStatus(elapsedTime))
	{
		CNLServerInfo::GetInstance()->ResetUpdsteElapsedTime();
	}
}

bool NSGateServerLocalAgentConnection::UpdateReportServerStatus(unsigned int elapsedTime)
{
	m_ACTimerReportStatus += elapsedTime;
	if (m_ACTimerReportStatus < m_DelayReport)
		return false;

	m_ACTimerReportStatus -= m_DelayReport;
	SendServerStatusToLocalAgent();
	return true;
}


void NSGateServerLocalAgentConnection::ChangeStatus(const std::string& status)
{
	m_MyStatus = status;
	SendStatusTransitionToLocalAgent();
}

void NSGateServerLocalAgentConnection::SendMessageToLocalAgent(const std::string& message)
{

	AgentJsonMessageToLocalAgent msg;
	msg.Message = message;

	FREE_FORMAT_JSON outPacket;
	outPacket.JsonDocument = msg.Dump();

	CNLOutStreamBuffer<8192> outStream;
	outStream << outPacket;

	this->Send(outStream.GetBuffer(), (cnl_packet_size_t)outStream.GetWriteOffset());
}

void NSGateServerLocalAgentConnection::SendStatusTransitionToLocalAgent()
{
	AgentJsonProcessStatusChanged changed;
	changed.Changed = m_MyStatus;

	FREE_FORMAT_JSON outPacket;
	outPacket.JsonDocument = changed.Dump();

	CNLOutStreamBuffer<8192> outStream;
	outStream << outPacket;

	this->Send(outStream.GetBuffer(), (cnl_packet_size_t)outStream.GetWriteOffset());

}

void NSGateServerLocalAgentConnection::SendHelloToLocalAgent()
{
	AgentJsonPayloadProcessHello hello;
	hello.ProcessType = "gate_server";
	hello.RunStatus = m_MyStatus;
	hello.ServerStartTime = CNLServerInfo::GetInstance()->GetServerStartTime();

	FREE_FORMAT_JSON outPacket;
	outPacket.JsonDocument = hello.Dump();
	CNLOutStreamBuffer<8192> outStream;
	outStream << outPacket;

	this->Send(outStream.GetBuffer(), (cnl_packet_size_t)outStream.GetWriteOffset());
}

void NSGateServerLocalAgentConnection::SendServerStatusToLocalAgent()
{
	AgentJsonGateServerProcessInfo info;
	info.ProcessType = "gate_server";
	info.RunStatus = m_MyStatus;

	FREE_FORMAT_JSON outPacket;
	outPacket.JsonDocument = info.Dump();
	CNLOutStreamBuffer<8192> outStream;
	outStream << outPacket;

	this->Send(outStream.GetBuffer(), (cnl_packet_size_t)outStream.GetWriteOffset());
}




NSGateServerLocalAgentConnection::NSGateServerLocalAgentConnection()
{
}

void NSGateServerLocalAgentConnection::OnRecvPacket(CNLNetEventQueue*, CNLPacketRef& packet)
{
	cnl_packet_size_t packetSize = 0;
	std::string packetType;

	CNLInStream inStream(packet.GetData(), packet.GetPacketLen());
	inStream >> packetSize >> packetType;
	inStream.ResetReadOffset();

	auto find = PacketHandlers.find(packetType);
	if (find == PacketHandlers.end())
	{
		LOGE << "Received unknown packet type " << packetType << " this packet will be discarded ";
		return;
	}

	(this->*(find->second))(packet.GetData(), packet.GetPacketLen());
}

void NSGateServerLocalAgentConnection::OnDisconnected(CNLNetEventQueue*, int32_t, const std::string&)
{
}

void NSGateServerLocalAgentConnection::OnAccepted(CNLNetEventQueue*)
{
	//로컬 에이전트에 대한 클라이언트 역할만 수행하므로 사용되지 않습니다.
}

void NSGateServerLocalAgentConnection::OnConnectSucceeded(CNLNetEventQueue*, const std::string&, unsigned short)
{
	LOGI << "\nConnected to local agent";
	//연결 완료시 hello 를 보내서 내가 누구인지 알립니다.
	this->SendHelloToLocalAgent();

}

void NSGateServerLocalAgentConnection::OnConnectFailed(CNLNetEventQueue*, const std::string&, unsigned short, int32_t, const std::string&)
{
	//LOGE << __FUNCTION__ << "\\Failed to connect to local agent\\" << why;
}

void NSGateServerLocalAgentConnection::OnRecvJson(char* dispatchThis, cnl_packet_size_t len)
{
	CNLInStream inStream(dispatchThis, len);
	cnl_packet_size_t dummySize = 0;
	std::string dummyPacketType;
	std::string jsonPayload;

	inStream >> dummySize >> dummyPacketType >> jsonPayload;

	std::string errParse;
	json11::Json json = json11::Json::parse(jsonPayload, errParse);

	//Json 이 이상합니다.
	if (errParse.empty() == false)
	{
		LOGE << "Failed to parse json payload " << errParse;
		return;
	}

	//인식 할 수 없는 Json payload type 입니다.
	auto findPayloadType = JsonPayloadHandlers.find(json["payload_type"].string_value());
	if (findPayloadType == JsonPayloadHandlers.end())
	{
		//LOGE << __FUNCTION__ << "Received unknown json payload type " << json["payload_type"].string_value() << " this payload will be discarded ";
		return;
	}

	json11::Json payload = json["payload"].object_items();
	if (payload.is_null() == true)
	{
		LOGE << "Received payload is null ";
		return;
	}

	(this->*(findPayloadType->second))(payload);

}

void NSGateServerLocalAgentConnection::OnRecvJsonTerminateProcess(json11::Json&)
{
	g_pcServerMain->SetServerState(ESERVER_STATE::RELEASE);

	//서비스 로직 플래그 세팅
	//메인 스레드 루프 break
	//메인 스레드 ServerMain 의 m_ReleaseCallback 호출됨 (MainServer.cpp g_releaseServer 로 세팅됨) 
	//메인 스레드 서비스 로직 스레드 join 이후 잔여 쿼리 처리 (NSDataBaseManager BlockAndWaitForRemainQuery 참조)
}

