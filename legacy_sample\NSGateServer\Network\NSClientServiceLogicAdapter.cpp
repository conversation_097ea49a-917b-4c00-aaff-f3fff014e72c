#include "stdafx.h"
#include "NSClientServiceLogicAdapter.h"
#include "NSClientHandler.h"

void NSClientServiceLogicAdapter::ForwardAcceptEventIntoThis(const std::shared_ptr<CNLIOInterfaceAdapter>& socketInterface)
{
	NSClientHandler::GetInstance()->ForwardedEventAccept(socketInterface);
}

void NSClientServiceLogicAdapter::ForwardRecvPacketEventIntoThis(int64_t socketInterfaceId, const char* buffer, int32_t size, bool allocateForCopy)
{
	NSClientHandler::GetInstance()->ForwardedEventRecvPacket(socketInterfaceId, buffer, size, allocateForCopy);
}

void NSClientServiceLogicAdapter::ForwardCloseEventIntoThis(int64_t socketInterfaceId, int32_t nativeErrorCode, const std::string& why)
{
	NSClientHandler::GetInstance()->ForwardedEventClose(socketInterfaceId, nativeErrorCode, why);
}

void NSClientServiceLogicAdapter::ForwardConnectSuccessEventIntoThis(const std::shared_ptr<CNLIOInterfaceAdapter>& socketInterface, const std::string& ip, unsigned short port)
{
	NSClientHandler::GetInstance()->ForwardedEventConnectSuccess(socketInterface, ip, port);
}

void NSClientServiceLogicAdapter::ForwardConnectFailEventIntoThis(const std::shared_ptr<CNLIOInterfaceAdapter>& socketInterface, const std::string& ip, unsigned short port, int32_t nativeErrorCode, const std::string& why)
{
	NSClientHandler::GetInstance()->ForwardedEventConnectFail(socketInterface, ip, port, nativeErrorCode, why);
}
