#include "stdafx.h"
#include "NSCommunityServerServiceLogicAdapter.h"
#include "NSCommunityServerHandler.h"

void NSCommunityServerServiceLogicAdapter::ForwardAcceptEventIntoThis(const std::shared_ptr<CNLIOInterfaceAdapter>& socketInterface)
{
	NSCommunityServerHandler::GetInstance()->ForwardedEventAccept(socketInterface);
}

void NSCommunityServerServiceLogicAdapter::ForwardRecvPacketEventIntoThis(int64_t socketInterfaceId, const char* buffer, int32_t size, bool allocateForCopy)
{
	NSCommunityServerHandler::GetInstance()->ForwardedEventRecvPacket(socketInterfaceId, buffer, size, allocateForCopy);
}

void NSCommunityServerServiceLogicAdapter::ForwardCloseEventIntoThis(int64_t socketInterfaceId, int32_t nativeErrorCode, const std::string& why)
{
	NSCommunityServerHandler::GetInstance()->ForwardedEventClose(socketInterfaceId, nativeErrorCode, why);
}

void NSCommunityServerServiceLogicAdapter::ForwardConnectSuccessEventIntoThis(const std::shared_ptr<CNLIOInterfaceAdapter>& socketInterface, const std::string& ip, unsigned short port)
{
	NSCommunityServerHandler::GetInstance()->ForwardedEventConnectSuccess(socketInterface, ip, port);
}
void NSCommunityServerServiceLogicAdapter::ForwardConnectFailEventIntoThis(const std::shared_ptr<CNLIOInterfaceAdapter>& socketInterface, const std::string& ip, unsigned short port, int32_t nativeErrorCode, const std::string& why)
{
	NSCommunityServerHandler::GetInstance()->ForwardedEventConnectFail(socketInterface, ip, port, nativeErrorCode, why);
}