#pragma once

#include "Module/NSGateModule.h"
#include "Version/NSProjectVersion.h"
#include "NSModels/NSModels.h"

class NSQueryData;
class NPPacketLoginReq;
class NSGateLoginModule : public NSGateModule
{
public:
	NSGateLoginModule();
	~NSGateLoginModule() override = default;

	void Init() override;

	bool OnAddSession(std::shared_ptr<NSClientSession> session) override;
	bool OnMoveSession(std::shared_ptr<NSClientSession> session) override;
	bool OnRemoveSession(std::shared_ptr<NSClientSession> session) override;
	bool OnDisconnect(std::shared_ptr<NSClientSession> session) override;

	static bool SendResultLoginAccount(std::shared_ptr<NSClientSession> session, EErrorCode errorCode);

	template <typename PacketType>
	static bool VersionCheck(PacketType packet);

private:
	void ProcessNPPacketLoginReq(std::shared_ptr<NSClientSession>& session, NPPacketLoginReq* packet);

public:
	//데이터베이스 처리
	static void ResultLoginAccount(const std::shared_ptr<NSQueryData> queryData);

	template <typename PacketType, typename Method>
	void RegisterMethod(Method method)
	{
		RegisterProcessor<PacketType>(this, method);
	}
};
