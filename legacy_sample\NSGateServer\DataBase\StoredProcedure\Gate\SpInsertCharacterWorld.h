#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpInsertCharacterWorld : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spInsertCharacterWorld";
	static constexpr EDataBase procedureHost = EDataBase::Common;
	EErrorCode MakeQuery(NSAdoCommand* command);

public:
	struct Input
	{
		int32_t Wid = 0;
		char PlatformID[g_uMaxPlatformIDLength]{ 0 };		
	} Input;

	SpInsertCharacterWorld() = default;
	SpInsertCharacterWorld(const int32_t wid,const std::string_view platformId);
};