#include "stdafx.h"
#include "NSAdoCommand.h"

#include "NSAdoRecordset.h"
#include "QueryData/NSPQueryData.h"
#include "NSDefineEnum.h"

NSAdoCommand::NSAdoCommand()
{
	m_pCommand.CreateInstance(__uuidof(Command));
	m_pCommand->CommandType = adCmdStoredProc;
	ZeroMemory(m_unicode, sizeof(m_unicode));
	m_szCommandName.clear();
}

NSAdoCommand::~NSAdoCommand()
{
	if (m_pCommand != nullptr)
	{
		if (m_pCommand->GetState() == adStateOpen)
			m_pCommand->Release();

		m_pCommand = nullptr;
	}
}

void NSAdoCommand::dump_com_error(const _com_error& e, const char* pFieldName)
{
	_bstr_t bstrSource(e.Source());
	_bstr_t bstrDescription(e.Description());

	std::string err = std::format("AdoCommand Error Code = {:#08x}, Code meaning = {}, Source = {}, Description = {}, command={}, FieldName={}",
		e.Error(), (LPCSTR)e.ErrorMessage(), (LPCSTR)bstrSource, (LPCSTR)bstrDescription, m_szCommandName.c_str(), pFieldName);

	LOGE << err;
	//LOGS << err;
}

bool NSAdoCommand::Execute(NSPQueryData* pcQueryData)
{
	if (pcQueryData == nullptr)
		return false;

	if (false == pcQueryData->SetAdoCommand(this))
		return false;

	NSAdoRecordset* pcAdoRecordSet = pcQueryData->GetAdoRecordSet(pcQueryData->GetQuerySetCount() - 1);
	if (pcAdoRecordSet == nullptr)
		return false;

	pcAdoRecordSet->SetCommandName(GetCommandName());

	m_iReturnValue = -100;

	try
	{
		m_pCommand->ActiveConnection = m_pConnection;
		if (m_pConnection->GetState() != adStateOpen)
			return false;

		//std::wstring queryString = GetQueryString();
		//LOGE << queryString;

		_RecordsetPtr pRs = m_pCommand->Execute(nullptr, nullptr, adCmdStoredProc);
		if (pRs->GetState() == adStateOpen)
		{
			pRs->PutRefActiveConnection(nullptr);
			pcAdoRecordSet->SetRecordset(pRs);
			pRs->Close();
		}
		GetItem("@ReturnValue", m_iReturnValue);
		pRs.Release();
		pRs = nullptr;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "EXECUTE ERROR");
		PrintQuery(m_iReturnValue, e.Error());

		//재접속 처리
		if ((e.Error() == 0x80004005 || e.Error() == 0x8007000e || e.Error() == 0x800A0E7D) && m_pConnection->GetState() == adStateOpen)
		{
			m_pConnection->Close();
		}
		return false;
	}
	catch (std::exception& e)
	{
		LOGE << std::format("Message : {}", e.what());
		return false;
	}

	pcQueryData->SetAdoRecordSet(GetCommandName(), pcAdoRecordSet);
	pcQueryData->SetReturnValue(pcQueryData->GetQuerySetCount() - 1, GetCommandName(), m_iReturnValue);

	ResultHandler();

	return true;
}

bool NSAdoCommand::Execute(NSAdoRecordset* pcAdoRecordSet)
{
	if (pcAdoRecordSet == nullptr)
		return false;

	m_iReturnValue = -100;

	try
	{
		m_pCommand->ActiveConnection = m_pConnection;
		if (m_pConnection->GetState() != adStateOpen)
			return false;

		_RecordsetPtr pRs = m_pCommand->Execute(nullptr, nullptr, adCmdStoredProc);
		if (pRs->GetState() == adStateOpen)
		{
			pRs->PutRefActiveConnection(nullptr);
			pcAdoRecordSet->SetRecordset(pRs);
			pRs->Close();
		}
		GetItem("@ReturnValue", m_iReturnValue);
		pRs.Release();
		pRs = nullptr;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "EXECUTE ERROR");
		PrintQuery(0, e.Error());

		//재접속 처리
		if ((e.Error() == 0x80004005 || e.Error() == 0x8007000e) && m_pConnection->GetState() == adStateOpen)
		{
			m_pConnection->Close();
		}
		return false;
	}
	catch (std::exception& e)
	{
		LOGE << std::format("Message : {}", e.what());
		return false;
	}

	ResultHandler();

	return true;
}

bool NSAdoCommand::Execute()
{
	m_iReturnValue = -100;

	try
	{
		m_pCommand->ActiveConnection = m_pConnection;
		if (m_pConnection->GetState() != adStateOpen)
			return false;


		_RecordsetPtr pRs = m_pCommand->Execute(nullptr, nullptr, adCmdStoredProc);
		if (pRs->GetState() == adStateOpen)
			pRs->Close();
		GetItem("@ReturnValue", m_iReturnValue);
		pRs.Release();
		pRs = nullptr;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "EXECUTE ERROR");
		PrintQuery(0, e.Error());

		//재접속 처리
		if ((e.Error() == 0x80004005 || e.Error() == 0x8007000e) && m_pConnection->GetState() == adStateOpen)
		{
			m_pConnection->Close();
		}
		return false;
	}
	catch (std::exception& e)
	{
		LOGE << std::format("Message : {}", e.what());
		return false;
	}

	ResultHandler();

	return true;
}

void NSAdoCommand::SetProcedure(_ConnectionPtr pConnection, char* strProcedure, int iTimeOut, bool bNamedParameter)
{
	m_pCommand->ActiveConnection = pConnection;
	m_pCommand->CommandText = strProcedure;
	m_pCommand->CommandTimeout = iTimeOut;
	m_pCommand->NamedParameters = bNamedParameter;
	m_pCommand->Prepared = false;
}

bool NSAdoCommand::SetParameter(const char* pParameter, DataTypeEnum vt, ParameterDirectionEnum emDt, size_t lSize)
{
	try
	{
		_variant_t vtValue;
		// adParamOutput 또는 adParamInputOutput 처리
		if (emDt == adParamOutput || emDt == adParamInputOutput)
		{
			switch (vt)
			{
			case adVarBinary:
			case adBinary:
			case adChar:
			case adVarChar:
			case adWChar:
			case adVarWChar:
			case adLongVarBinary:
			{
				vt = adBSTR;
				vtValue = SysAllocString(L"");
				m_pCommand->Parameters->Append(m_pCommand->CreateParameter(pParameter, vt, emDt, lSize, vtValue));
			}
			break;
			case adLongVarChar:
			case adLongVarWChar:
			{
				vtValue = SysAllocString(L"");
				m_pCommand->Parameters->Append(m_pCommand->CreateParameter(pParameter, vt, emDt, std::max(lSize, (size_t)8192), vtValue));
			}
			break;
			default:
			{
				vtValue = _variant_t();
				m_pCommand->Parameters->Append(m_pCommand->CreateParameter(pParameter, vt, emDt, lSize, vtValue));
			}
			break;
			}
		}
		else // adParamInput 처리
		{
			switch (vt)
			{
			case adVarBinary:
			case adBinary:
			case adChar:
			case adVarChar:
			case adLongVarChar:
			case adWChar:
			case adVarWChar:
			case adLongVarWChar:
			case adLongVarBinary:
			{
				m_pCommand->Parameters->Append(m_pCommand->CreateParameter(pParameter, vt, emDt, lSize));
			}
			break;
			default:
			{
				m_pCommand->Parameters->Append(m_pCommand->CreateParameter(pParameter, vt, emDt, lSize));
			}
			break;
			}
		}
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pParameter);
		return false;
	}
	return true;
}


void NSAdoCommand::SetConnection(_ConnectionPtr pConnection)
{
	m_pConnection = pConnection;
}

void NSAdoCommand::Reset()
{
	try
	{
		long iSize = (int)m_pCommand->Parameters->Count;
		for (long i = 0; i < iSize; ++i)
		{
			_ParameterPtr pcParam = m_pCommand->Parameters->GetItem(i);
			pcParam->GetValue().Clear();
		}
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "RESET ERROR");
	}
}

bool NSAdoCommand::SetItem(const char* pFieldName, const char* pValue)
{
	try
	{
		switch (m_pCommand->GetParameters()->Item[pFieldName]->GetType())
		{
		case adChar:
		case adVarChar:
		case adBinary:
		case adVarBinary:
		{
			BSTR bstrValue = _com_util::ConvertStringToBSTR(pValue);
			m_pCommand->GetParameters()->Item[pFieldName]->Value = bstrValue;
			SysFreeString(bstrValue);
		}
		break;
		case adWChar:
		case adVarWChar:
		{
			if (strlen(pValue) > ADORECORDSET_UNICODE_BUFFER_SIZE)
			{
				LOGF << std::format("Buffer size overflow. MAX {} bytes. [{}]", ADORECORDSET_UNICODE_BUFFER_SIZE, pFieldName);
				return false;
			}
			MultiByteToWideChar(CP_UTF8, 0, pValue, -1, m_unicode, ADORECORDSET_UNICODE_BUFFER_SIZE);
			BSTR bstr = SysAllocString(m_unicode);
			m_pCommand->GetParameters()->Item[pFieldName]->Value = bstr;
			SysFreeString(bstr);
		}
		break;
		}
	}
	catch (const _com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::SetItem(const char* pFieldName, const bool bValue)
{
	uint8_t uValue = (bValue == true) ? 1 : 0;

	return SetItem(pFieldName, uValue);
}

bool NSAdoCommand::SetItem(const char* pFieldName, const uint8_t uValue)
{
	try
	{
		m_pCommand->GetParameters()->Item[pFieldName]->Value = uValue;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::SetItem(const char* pFieldName, const uint16_t uValue)
{
	try
	{
		m_pCommand->GetParameters()->Item[pFieldName]->Value = uValue;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::SetItem(const char* pFieldName, const uint32_t uValue)
{
	try
	{
		m_pCommand->GetParameters()->Item[pFieldName]->Value = uValue;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::SetItem(const char* pFieldName, const uint32_t uIndex, const uint16_t uValue)
{
	char strFieldName[128] = {
		0,
	};
	sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

	return SetItem(strFieldName, uValue);
}

bool NSAdoCommand::SetItem(const char* pFieldName, const uint32_t uIndex, const int iValue)
{
	char strFieldName[128] = {
		0,
	};
	sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

	return SetItem(strFieldName, iValue);
}

bool NSAdoCommand::SetItem(const char* pFieldName, const uint32_t uIndex, const uint32_t uValue)
{
	char strFieldName[128] = {
		0,
	};
	sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

	return SetItem(strFieldName, uValue);
}

bool NSAdoCommand::SetItem(const char* pFieldName, const int iValue)
{
	try
	{
		m_pCommand->GetParameters()->Item[pFieldName]->Value = iValue;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::SetItem(const char* pFieldName, const int64_t iValue)
{
	try
	{
		_variant_t vt;
		vt.vt = VT_I8;
		vt.llVal = iValue;
		m_pCommand->GetParameters()->Item[pFieldName]->Value = vt;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::SetItem(const char* pFieldName, const uint64_t uValue)
{
	try
	{
		_variant_t vt;
		vt.vt = VT_I8;
		vt.llVal = uValue;
		m_pCommand->GetParameters()->Item[pFieldName]->Value = vt;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::SetItem(const char* pFieldName, const float fValue)
{
	try
	{
		_variant_t vt;
		vt.vt = VT_R4;
		vt.fltVal = fValue;
		m_pCommand->GetParameters()->Item[pFieldName]->Value = vt;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::SetItem(const char* pFieldName, const uint32_t uIndex, const uint64_t uValue)
{
	char strFieldName[128] = {
		0,
	};
	sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

	return SetItem(strFieldName, uValue);
}

bool NSAdoCommand::SetItem(const char* pFieldName, _variant_t& value)
{
	try
	{
		m_pCommand->GetParameters()->Item[pFieldName]->Value = value;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::SetItem(const char* pFieldName, SYSTEMTIME& sysTime)
{
	try
	{
		_variant_t vt;
		vt.vt = VT_DATE;
		SystemTimeToVariantTime(&sysTime, &vt.date);
		m_pCommand->GetParameters()->Item[pFieldName]->Value = vt;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::SetItem(const char* pFieldName, NPDateTime& dtTime)
{
	try
	{
		SYSTEMTIME sysTime = NSUtil::ConvertDateTimeToSystemTime(dtTime);

		_variant_t vt;
		vt.vt = VT_DATE;
		SystemTimeToVariantTime(&sysTime, &vt.date);
		m_pCommand->GetParameters()->Item[pFieldName]->Value = vt;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::SetItemBinary(const char* pFieldName, byte* pValue, int iSize)
{
	try
	{
		if (!iSize)
			return true;

		// SAFEARRAY 선언
		SAFEARRAY FAR* psa;
		SAFEARRAYBOUND rgsabound[1];
		rgsabound[0].lLbound = 0;
		rgsabound[0].cElements = iSize;

		// 스트림데이터 저장
		psa = SafeArrayCreateVector(VT_UI1, 1, iSize);

		unsigned char* pData = nullptr;

		//잠긴어레이에서 데이터 포인터를 얻어온다.
		SafeArrayAccessData(psa, (void**)&pData);

		//SAFEARRAY에 데이터 복사
		memcpy(pData, pValue, iSize);

		//SAFEARRAY를 풀어준다.
		SafeArrayUnaccessData(psa);

		_variant_t vt;
		vt.vt = VT_ARRAY | VT_UI1;
		vt.parray = psa;

		m_pCommand->GetParameters()->Item[pFieldName]->Value = vt;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::GetItem(const char* pFieldName, bool& bValue)
{
	uint8_t iValue = 0;
	bool	bResult = GetItem(pFieldName, iValue);

	bValue = (iValue != 0) ? true : false;

	return bResult;
}

bool NSAdoCommand::GetItem(const char* pFieldName, uint16_t& iValue)
{
	try
	{
		iValue = m_pCommand->GetParameters()->GetItem(pFieldName)->GetValue().uiVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::GetItem(const char* pFieldName, uint32_t& iValue)
{
	try
	{
		iValue = m_pCommand->GetParameters()->GetItem(pFieldName)->GetValue().uintVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::GetItem(const char* pFieldName, uint64_t& iValue)
{
	try
	{
		iValue = m_pCommand->GetParameters()->GetItem(pFieldName)->GetValue().llVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::GetItem(const char* pFieldName, const uint32_t uIndex, uint8_t& iValue)
{
	try
	{
		char strFieldName[128] = {
			0,
		};
		sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

		iValue = m_pCommand->GetParameters()->GetItem(strFieldName)->GetValue().bVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::GetItem(const char* pFieldName, const uint32_t uIndex, uint16_t& iValue)
{
	try
	{
		char strFieldName[128] = {
			0,
		};
		sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

		iValue = m_pCommand->GetParameters()->GetItem(strFieldName)->GetValue().uiVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::GetItem(const char* pFieldName, const uint32_t uIndex, int32_t& iValue)
{
	try
	{
		char strFieldName[128] = {
			0,
		};
		sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

		iValue = m_pCommand->GetParameters()->GetItem(strFieldName)->GetValue().intVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::GetItem(const char* pFieldName, const uint32_t uIndex, uint32_t& iValue)
{
	try
	{
		char strFieldName[128] = {
			0,
		};
		sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

		iValue = m_pCommand->GetParameters()->GetItem(strFieldName)->GetValue().uintVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::GetItem(const char* pFieldName, const uint32_t uIndex, uint64_t& iValue)
{
	try
	{
		char strFieldName[128] = {
			0,
		};
		sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

		iValue = m_pCommand->GetParameters()->GetItem(strFieldName)->GetValue().llVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::GetItem(const char* pFieldName, uint8_t& iValue)
{
	try
	{
		iValue = m_pCommand->GetParameters()->GetItem(pFieldName)->GetValue().bVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::GetItem(const char* pFieldName, int& iValue)
{
	try
	{
		iValue = m_pCommand->GetParameters()->GetItem(pFieldName)->GetValue().intVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::GetItem(const char* pFieldName, int64_t& liValue)
{
	try
	{
		liValue = m_pCommand->GetParameters()->GetItem(pFieldName)->GetValue().llVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::GetItem(const char* pFieldName, float& fValue)
{
	try
	{
		fValue = m_pCommand->GetParameters()->GetItem(pFieldName)->GetValue().fltVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}
	return true;
}

bool NSAdoCommand::GetItem(const char* pFieldName, NPDateTime& dtTime)
{
	try
	{
		SYSTEMTIME sysTime;

		_variant_t vtSrcValue = m_pCommand->GetParameters()->GetItem(pFieldName)->Value;
		_variant_t vtDstValue;
		if (VariantChangeTypeEx(&vtDstValue, &vtSrcValue, GetUserDefaultLCID(), 0, VT_DATE) != S_OK)
			return false;
		VariantTimeToSystemTime(vtDstValue.date, &sysTime);
		VariantClear(&vtDstValue);
		dtTime.year = sysTime.wYear;
		dtTime.month = sysTime.wMonth;
		dtTime.day = sysTime.wDay;
		dtTime.hour = sysTime.wHour;
		dtTime.minute = sysTime.wMinute;
		dtTime.second = sysTime.wSecond;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}
	return true;
}

bool NSAdoCommand::GetItem(const char* pFieldName, int8_t& iValue)
{
	try
	{
		iValue = m_pCommand->GetParameters()->GetItem(pFieldName)->GetValue().bVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::GetItem(const char* pFieldName, char* pValue, int iSize)
{
	try
	{
		DataTypeEnum eDataType = m_pCommand->GetParameters()->GetItem(pFieldName)->GetType();
		if (adBSTR != eDataType)
			return false;

		wcsncpy_s(m_unicode, (LPWSTR)(_bstr_t(m_pCommand->GetParameters()->GetItem(pFieldName)->GetValue().bstrVal)), iSize);
		::WideCharToMultiByte(CP_UTF8, 0, m_unicode, -1, pValue, iSize, nullptr, nullptr);
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::GetItemUTF8(const char* pFieldName, char* pValue, int iSize)
{
	try
	{
		DataTypeEnum eDataType = m_pCommand->GetParameters()->GetItem(pFieldName)->GetType();
		if (adBSTR != eDataType)
			return false;
		std::wstring str((LPWSTR)(_bstr_t(m_pCommand->GetParameters()->GetItem(pFieldName)->GetValue().bstrVal)));
		str = str.erase(str.find_last_not_of(L" \t\r\n") + 1);
		WideCharToMultiByte(CP_UTF8, 0, str.c_str(), -1, pValue, iSize - 1, nullptr, nullptr);
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

bool NSAdoCommand::GetItemBinary(char* pData, byte* pValue, int iSize, const char* pFieldName)
{
	try
	{
		if (!iSize)
			return true;

		// SAFEARRAY 선언
		SAFEARRAY FAR* psa;
		SAFEARRAYBOUND rgsabound[1];
		rgsabound[0].lLbound = 0;
		rgsabound[0].cElements = iSize;

		// 스트림데이터 저장
		psa = SafeArrayCreateVector(VT_UI1, 1, iSize);

		//잠긴어레이에서 데이터 포인터를 얻어온다.
		SafeArrayAccessData(psa, (void**)&pData);

		//SAFEARRAY에 데이터 복사
		memcpy(pData, pValue, iSize);

		//SAFEARRAY를 풀어준다.
		SafeArrayUnaccessData(psa);

		//삭제
		SafeArrayDestroyData(psa);
	}
	catch (_com_error& e)
	{
		dump_com_error(e, pFieldName);
		return false;
	}

	return true;
}

auto NSAdoCommand::GetQueryString(std::string& query) -> const char*
{
	// GetQueryString은 string으로 수정하면 좋을 듯, _bstr_t 사용으로 바로 string으로 사용하면 됨
	query = _bstr_t{ GetQueryString().c_str() };
	return query.c_str();
}

void NSAdoCommand::PrintQuery()
{
	LOGD << GetQueryString();
}

std::wstring NSAdoCommand::GetQueryString()
{
	std::wstring queryString;
	queryString += L"CALL ";

	std::wstring command(m_szCommandName.begin(), m_szCommandName.end());
	queryString += command;
	queryString += L" ";

	try
	{
		long paramCount = m_pCommand->Parameters->GetCount();
		for (short index = 0; index < paramCount; ++index)
		{
			_variant_t Index;
			Index.vt = VT_I2;
			Index.iVal = index;
			_ParameterPtr param = m_pCommand->Parameters->GetItem(Index);
			bool         bQuotation = false;
			DataTypeEnum eDataType = param->GetType();
			switch (eDataType)
			{
			case adVarBinary:
			case adBinary:
			case adChar:
			case adVarChar:
			case adLongVarChar:
			case adWChar:
			case adVarWChar:
			case adLongVarWChar:
			case adLongVarBinary:
			{
				bQuotation = true;
				break;
			}
			}
			if (param->GetDirection() == ParameterDirectionEnum::adParamInput)
			{
				_bstr_t str = static_cast<_bstr_t>(param->GetValue());
				std::wstring ws(str, SysStringLen(str));
				if (bQuotation)
					queryString += L"'";
				queryString += ws;
				if (bQuotation)
					queryString += L"'";
				if (index < paramCount - 1)
					queryString += L", ";
			}
			else if (param->GetDirection() == ParameterDirectionEnum::adParamReturnValue)
			{
				_bstr_t      str = param->GetName();
				std::wstring ws(str, SysStringLen(str));
				queryString += ws;
				queryString += L" = ";
			}
			else
			{
				_bstr_t      str = param->GetName();
				std::wstring ws(str, SysStringLen(str));
				queryString += ws;
				if (index < paramCount - 1)
					queryString += L", ";
			}
		}
	}
	catch (const _com_error& e)
	{
		LOGE << std::format(L"{}", e.ErrorMessage());
		return L"";
	}
	return queryString;
}
void NSAdoCommand::ResultHandler(long lError)
{
	if (m_iReturnValue != 0)
	{
		// 열거형 값의 범위
		const int32_t minValue = EProcReturnValue::GuildInviteNotFound;
		const int32_t maxValue = EProcReturnValue::CharacterError;

		// DB 에러 타입인지 확인
		switch (m_iReturnValue)
		{
		case EProcReturnValue::DBLogicError:
		case EProcReturnValue::DBSequenceUpdateError:
		case EProcReturnValue::DBFatalError:
			PrintQuery(m_iReturnValue, lError);
			break;
		default:
			// 열거형 값 범위에 속하지 않는 경우 PrintQuery 호출
			if (m_iReturnValue < minValue || m_iReturnValue > maxValue)
			{
				PrintQuery(m_iReturnValue, lError);
			}
			break;
		}
	}
}
void NSAdoCommand::PrintQuery(int iReturnValue, long lError)
{
	std::wstring queryString = GetQueryString();
	std::wstring errorstr;

	if (iReturnValue == 0 && lError == 0)
	{
		errorstr = std::format(L"msg=Database query execution failed, Query=({})", queryString);
	}
	else
	{
		errorstr = std::format(L"msg=Database query execution failed, Query=({}), ReturnValue={}, ErrorCode={:x}", queryString, iReturnValue, lError);
	}

	LOGE << errorstr;
	//LOGS << errorstr;
	//LOGS << std::format(L"#99_db_alert#{}", errorstr);
}
