#include "stdafx.h"
#include "NSStruct.h"

void NSCompletedQuestStepData::Clear()
{
	QuestStepCodes[0] = 0;
}

bool NSCompletedQuestStepData::AddStepCode(char stepCode)
{
	for (int i = 0; i < g_uMaxCompletedQuestStepCount - 1; ++i)
	{
		if (QuestStepCodes[i] == stepCode)
			break;

		if (QuestStepCodes[i] == 0)
		{
			QuestStepCodes[i] = stepCode;
			QuestStepCodes[i + 1] = 0;
			return true;
		}
	}

	return false;
}

bool NSCompletedQuestStepData::IsStepCompleted(int32_t questStepId) const
{
	char completedStepCode = MakeStepCode(QuestId, questStepId);
	for (const char stepCode : QuestStepCodes)
	{
		if (stepCode == 0)
			break;

		if (stepCode == completedStepCode)
			return true;
	}

	return false;
}

auto NSCompletedQuestStepData::MakeStepCode(int32_t questId, int32_t questStepId) -> char
{
	return static_cast<char>(questStepId - (questId * 100) + 1);
}

auto NSCompletedQuestStepData::GetQuestStepIdFromStepCode(int32_t questId, char stepCode) ->int32_t
{
	return (questId * 100) + stepCode - 1;
}
