#include "stdafx.h"
#include <CNLCoreIO.h>

#include "NSCommunityServerSession.h"
#include "NPPacket.h"

std::unordered_map<uint16_t, NSCommunityServerSession::PROCESSHANDLE> NSCommunityServerSession::m_PacketHandler
{
};

NSCommunityServerSession::NSCommunityServerSession()
{
}

NSCommunityServerSession::~NSCommunityServerSession()
{
}

void NSCommunityServerSession::Reset()
{
	m_SocketChannel = nullptr;
}

int64_t NSCommunityServerSession::GetSocketChannelId() const
{
	if (m_SocketChannel == nullptr)
		return 0;

	return m_SocketChannel->GetChannelID();
}

void NSCommunityServerSession::SetChannel(const std::shared_ptr<CNLIOInterfaceAdapter>& channel)
{
	if (channel == nullptr)
	{
		assert(false);
		return;
	}

	m_SocketChannel = channel;
}

const std::shared_ptr<CNLIOInterfaceAdapter>& NSCommunityServerSession::GetChannel() const
{
	return m_SocketChannel;
}

void NSCommunityServerSession::Close() const
{
	if (m_SocketChannel)
	{
		m_SocketChannel->Close(nullptr);
	}
}

void NSCommunityServerSession::OnRecv(NSPacket* packet, [[maybe_unused]] NSPacketData* packetData)
{
	if (m_SocketChannel == nullptr)
		return;

	auto type = static_cast<uint16_t>(packet->GetType());
	if (m_PacketHandler.find(type) != m_PacketHandler.end())
	{
		auto session = shared_from_this();
		m_PacketHandler[type](session, packet);
	}
}

void NSCommunityServerSession::Send(const char* buffer, int size)
{
	if (m_SocketChannel == nullptr)
		return;
	m_SocketChannel->Send(buffer, (cnl_packet_size_t)size);
}

void NSCommunityServerSession::RegisterPacketHandler(std::function<void(std::shared_ptr<NSCommunityServerSession>&, NSPacket*)> func, uint16_t type)
{
	if (type >= static_cast<uint16_t>(EServerPacketType::End))
		return;

	m_PacketHandler[type] = func;
}
