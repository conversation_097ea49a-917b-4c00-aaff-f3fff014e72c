#pragma once

// MySQL 버전별 호환성 처리
// MySQL 8.0에서 my_bool이 제거되고 bool로 변경됨

#include <mysql.h>

// MySQL 버전 체크
#if defined(LIBMYSQL_VERSION_ID) && LIBMYSQL_VERSION_ID >= 80000
    // MySQL 8.0 이상
    using mysql_bool_t = bool;
#else
    // MySQL 5.7 이하 또는 MariaDB
    #ifdef my_bool
        using mysql_bool_t = my_bool;
    #else
        // MariaDB Connector/C 3.3+도 bool 사용
        using mysql_bool_t = bool;
    #endif
#endif

// 호환성을 위한 타입 정의
// int를 사용하면 모든 버전에서 호환 가능
using mysql_bool_compat = int;