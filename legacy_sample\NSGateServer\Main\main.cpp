#include "stdafx.h"
#include "Main/MainServer.h"
#include "ServerMain/NSServerMain.h"
#include "ServerMain/NSServerMainConsole.h"
#include "ServerMain/NSServerMainService.h"
#include "NSDefine.h"
#include "Consul/Consul.h"
#include "Logger/NSLoggerBuidler.h"
#include "LocalAgent/LocalAgentDS.h"

#include "Clock/NSIntervalScheduler.h"

#include <shellapi.h>

extern void InitializeLog(const EServerType serverType);

int WINAPI wWinMain(_In_ HINSTANCE hInstance, [[maybe_unused]] _In_opt_ HINSTANCE hPrevInstance, [[maybe_unused]] _In_ LPWSTR lpCmdLine, [[maybe_unused]] _In_ int nCmdShow)
{
#ifdef _DEBUG
	_CrtSetDbgFlag(_CRTDBG_ALLOC_MEM_DF | _CRTDBG_LEAK_CHECK_DF | _CRTDBG_REPORT_FLAG);
	//	_CrtSetBreakAlloc(19190824);

	// debug 함수군들의 report type을 Output Debug 창으로 한다.
	_CrtSetReportMode(_CRT_WARN, _CRTDBG_MODE_DEBUG);
	_CrtSetReportMode(_CRT_ERROR, _CRTDBG_MODE_DEBUG);
	_CrtSetReportMode(_CRT_ASSERT, _CRTDBG_MODE_DEBUG);
#endif

	//기본 WorkDirectory 설정
	std::string currentModuelPath;
	NSUtil::GetCurrentModuleFilePath(currentModuelPath, true);
	SetCurrentDirectoryA(currentModuelPath.c_str());

	int32_t instantId{ 0 };
	NSConfigDocument configOverride;

	std::string bootstrap;
	std::string processClassName;

	int argc{ 0 };
	LPWSTR* argv = ::CommandLineToArgvW(GetCommandLineW(), &argc);
	if (argv != nullptr)
	{
		for (int i = 0; i < argc; i++)
		{
			std::wstring arg{ argv[i] };

			if (!arg.compare(L"bootstrap"))
			{
				if (i + 1 >= argc)
					continue;
				++i;
				bootstrap = NSUtil::WideToMB(argv[i], CP_ACP);
			}

			if (arg.compare(L"gameport") == 0)
			{
				if (i + 1 >= argc)
					continue;
				++i;

				std::wstring port{ argv[i] };
				configOverride.SetValue("GateServer", "WorldPort", static_cast<uint16_t>(std::stoul(port)));
			}

			if (arg.compare(L"clientport") == 0)
			{
				if (i + 1 >= argc)
					continue;
				++i;

				std::wstring port{ argv[i] };
				configOverride.SetValue("GateServer", "ClientPort", static_cast<uint16_t>(std::stoul(port)));
				instantId = std::stoul(port);
			}

			if (arg.compare(L"apiport") == 0)
			{
				if (i + 1 >= argc)
					continue;
				++i;

				std::wstring port{ argv[i] };
				configOverride.SetValue("GateServer", "APIPort", static_cast<uint16_t>(std::stoul(port)));
			}

			if (arg.compare(L"processclassname") == 0)
			{
				if (i + 1 >= argc)
					continue;

				processClassName = NSUtil::WideToMB(argv[i + 1], CP_ACP);
				++i;
			}

		}
	}

	int32_t exitCode = Bootstrap{}
		.Add("System Initialize", [instantId](std::ofstream& stream)
			{
				if (!System::GetInstance()->Build(EServerType::GateServer, EWorldServerType::None, instantId))
				{
					stream << "System Initialize Fail!" << std::endl;
					return 2929;
				}

				stream << System::GetInstance()->GetDocument().GetConfig().dump(4) << std::endl;
				return 0;
			})
		.Add("Log Initialize", [](std::ofstream& stream)
			{
				InitializeLog(EServerType::GateServer);
				stream << "Log Initialize Success!" << std::endl;
				return 0;
			})
		.Add("Service Initialize", [instantId](std::ofstream& stream)
			{
				if (!Service::GetInstance()->Build(EServerType::GateServer, EWorldServerType::None, instantId))
				{
					stream << "Service Initialize Fail!" << std::endl;
					return 2929;
				}

				LOGI << "Service Initialize";
				stream << Service::GetInstance()->GetDocument().GetConfig().dump(4) << std::endl;
				return 0;
			})
		.Add("Config Initialize", [](std::ofstream& stream)
			{
				std::string configString;
				if (!Service::GetInstance()->GetServerConfig(configString))
				{
					stream << "Failed to get config from Consul." << std::endl;
					return 2931;
				}

				if (!NSConfigManager::GetInstance()->LoadFromString(configString.c_str()))
				{
					stream << "Failed to load config from string." << std::endl;
					return 2931;
				}

				std::string worldConfigKey = std::format("/World_Config/{}", Service::GetInstance()->GetWid());
				nlohmann::json find = NSConfigManager::GetInstance()->GetDocument().GetValue(worldConfigKey, nlohmann::json{});
				if (!find.empty())
				{
					stream << worldConfigKey << " Found!" << std::endl;
					if (!NSConfigManager::GetInstance()->Merge(find))
					{
						stream << "Failed to load config from string." << std::endl;
						return 2931;
					}

					NSConfigManager::GetInstance()->GetDocument().Erase("World_Config");
				}

				LOGI << "Config Initialize";
				stream << NSConfigManager::GetInstance()->GetDocument().GetConfig().dump(4) << std::endl;
				return 0;
			})
		.Add("Config Override (local file)", [](std::ofstream& stream)
			{
				std::string filename = NSUtil::GetConfigOverridingFile(NSUtil::GetConfigFilePath("json"));
				std::ifstream file(filename);
				if (!file.good())
				{
					stream << "Override step skipped." << std::endl;
					return 0;
				}

				if (!NSConfigManager::GetInstance()->LoadFromFile(filename.c_str()))
				{
					stream << std::format("Failed to load config from {}.", filename) << std::endl;
					return 2931;
				}

				LOGI << "Config Override(file):" << filename;
				stream << NSConfigManager::GetInstance()->GetDocument().GetConfig().dump(4) << std::endl;
				return 0;
			})
		.Add("Config Override (arguments)", [&configOverride](std::ofstream& stream)
			{
				if (configOverride.GetConfig().empty())
				{
					stream << "Override step skipped." << std::endl;
					return 0;
				}

				if (!NSConfigManager::GetInstance()->LoadFromString(configOverride.GetConfig().dump().c_str()))
				{
					stream << "Failed to load config from Arguments." << std::endl;
					return 2931;
				}

				LOGI << "Config Override(arguments)";
				stream << NSConfigManager::GetInstance()->GetDocument().GetConfig().dump(4) << std::endl;
				return 0;
			})
		.Add("Service Register", [](std::ofstream& stream)
			{
				const ServerConfig& config = NSConfigManager::GetInstance()->GetConfigs();

				std::vector<std::string> tags{ Service::GetInstance()->GetWorldGroup(), std::to_string(Service::GetInstance()->GetWid()) };
				nlohmann::json data = Service::GetInstance()->CreateServiceInfo(tags, config.GateServer.APIPort, config);

				bool updated{ false };
				if (!Service::GetInstance()->AutoRegister(EServerType::GateServer, data, updated))
				{
					stream << "Service Register Fail" << std::endl;
				}

				if (!updated)
				{
					stream << "Service Register Skip" << std::endl;
				}

				LOGI << "Service Register";
				stream << data.dump(4) << std::endl;
				return 0;
			})
		.Run(bootstrap, true);

	if (exitCode != 0)
	{
		return exitCode;
	}

	NSIntervalScheduler{}.Run([] { LOGI << "Log every 1 hour"; }, std::chrono::hours(1));

	// 최소 클라이언트 버전 얻기
	Service::GetInstance()->UpdateMinClientVer();

	if (!processClassName.empty())
		LocalAgentGlobalVariables::SetThisProcessClassName(processClassName);

	//윈도우 서비스 모드 동작 여부 확인
	if (NSConfigManager::GetConfigs().Common.Application.Service == 0)
	{
		// Console로 구동
		g_pcServerMain = new NSServerMainConsole(hInstance, false, true);
	}
	else
	{
		// Service로 구동
		g_pcServerMain = new NSServerMainService(hInstance);
	}

	if (g_pcServerMain)
	{
		if (g_pcServerMain->InitServer(EServerType::GateServer, g_InitServer, g_ProcessServer, g_ReleaseServer) == false)
		{
			LOGE << "Server Initialize Fail!";
			delete g_pcServerMain;
			g_pcServerMain = nullptr;
			exit(1);
		}
		else
		{
			if (g_pcServerMain->GetServerServiceType() != EServerServiceType::Service)
			{
				g_pcServerMain->Process();
			}

			delete g_pcServerMain;
			g_pcServerMain = nullptr;
		}
	}
	else
	{
		LOGF << "g_pcServerMain Allocation Fail";
		exit(1);
	}

	return 0;
}
