#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpSelectNotices : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spSelectNotices";
	static constexpr EDataBase procedureHost = EDataBase::Common;
	EErrorCode MakeQuery(NSAdoCommand* command);

public:
	struct Input
	{
		int32_t Wid = 0;
	} Input;

	SpSelectNotices() = default;
	SpSelectNotices(const int32_t wid);
};