#include "stdafx.h"
#include "Profiler/NSProfiler.h"
#include "Logger/NSLogger.h"

#include "NSStorageManager.h"

#include "NSTickManager/NSTickManager.h"

NSStorageManager::~NSStorageManager()
{}

void NSStorageManager::Process()
{
	NS_PROFILE(__FUNCTION__);
	uint64_t now = NSTickManager::GetInstance()->GetUnixTimeStamp();
	if (now < m_NextUpdateTick)
		return;

	//LOGD << std::format("NSStorageManager::Process() started.");
	m_NextUpdateTick = now + 60000;

	auto it = m_ExpiredByCid.begin();
	while (it != m_ExpiredByCid.end())
	{
		int64_t cid = it->first;
		uint64_t expireTimestamp = it->second;

		if (now < expireTimestamp)
		{
			++it;
		}
		else
		{
			RemoveData(cid);
			it = m_ExpiredByCid.erase(it);
		}
	}
}

bool NSStorageManager::IsSelectPossible(int64_t cid) const
{
	auto it_pending = m_PendingSequenceByCid.find(cid);
	auto it_updated = m_UpdatedSequenceByCid.find(cid);

	if (it_pending == m_PendingSequenceByCid.end() ||
		it_updated == m_UpdatedSequenceByCid.end())
		return false;

	return it_pending->second == it_updated->second;
}

bool NSStorageManager::IsUpdatePossible(int64_t cid) const
{
	return GetFailCount(cid) < MAX_ATTEMPT_COUNT;
}

bool NSStorageManager::IsRollbackPossible(int64_t cid, size_t modelCount) const
{
	auto it_pending = m_PendingSequenceByCid.find(cid);
	auto it_updated = m_UpdatedSequenceByCid.find(cid);

	if (it_pending == m_PendingSequenceByCid.end() ||
		it_updated == m_UpdatedSequenceByCid.end())
		return false;

	if (it_pending->second - static_cast<int64_t>(modelCount) != it_updated->second)
		return false;

	return true;
}

const int64_t NSStorageManager::GetNextSequence(int64_t cid)
{
	auto it_pending = m_PendingSequenceByCid.find(cid);
	if (it_pending == m_PendingSequenceByCid.end())
	{
		LOGE << std::format("NSStorageManager::GetNextSequence() cid {} does not exist PendingSequence", cid);
		return -1;
	}

	Touch(cid);
	it_pending->second += 1;

	int64_t pendingSeq = it_pending->second;
	LOGD << std::format("NSStorageManager::GetNextSequence() Cid: {}, PendingSeq: {}", cid, pendingSeq);

	return pendingSeq;
}

const int64_t NSStorageManager::GetUpdatedSequence(int64_t cid) const
{
	int64_t updatedSeq = 0;
	{
		auto it = m_UpdatedSequenceByCid.find(cid);
		if (it != m_UpdatedSequenceByCid.end())
		{
			updatedSeq = it->second;
		}
		else
		{
			LOGE << std::format("NSStorageManager::GetUpdatedSequence() cid {} does not exists.", cid);
		}
	}

	return updatedSeq;
}

const int64_t NSStorageManager::GetPendingSequence(int64_t cid) const
{
	auto it_pending = m_PendingSequenceByCid.find(cid);
	auto it_updated = m_UpdatedSequenceByCid.find(cid);

	if (it_pending == m_PendingSequenceByCid.end() ||
		it_updated == m_UpdatedSequenceByCid.end())
		return 0;

	if (it_pending->second == it_updated->second)
		return 0;

	return it_pending->second;
}

bool NSStorageManager::RollbackPendingSequence(int64_t cid, size_t modelCount)
{
	auto it_pending = m_PendingSequenceByCid.find(cid);
	auto it_updated = m_UpdatedSequenceByCid.find(cid);

	if (it_pending == m_PendingSequenceByCid.end() ||
		it_updated == m_UpdatedSequenceByCid.end())
	{
		// error
		return false;
	}

	if (it_pending->second - static_cast<int64_t>(modelCount) != it_updated->second)
	{
		return false;
	}

	LOGD << std::format("NSStorageManager::RollbackPendingSequence() cid: {}, Pending: {}, CurrentSeq: {}, RollbackStep: {}",
		cid, it_pending->second, it_updated->second, static_cast<int64_t>(modelCount));

	it_pending->second = it_updated->second;

	LOGD << std::format("NSStorageManager::RollbackPendingSequence() cid: {}, Pending: {}, CurrentSeq: {}, RollbackStep: {}",
		cid, it_pending->second, it_updated->second, static_cast<int64_t>(modelCount));

	return true;
}

bool NSStorageManager::SetUpdatedSequence(int64_t cid, int64_t updatedSeq)
{
	auto it_pending = m_PendingSequenceByCid.find(cid);
	auto it_updated = m_UpdatedSequenceByCid.find(cid);
	auto it_failed = m_FailByCid.find(cid);

	if (it_pending == m_PendingSequenceByCid.end() ||
		it_updated == m_UpdatedSequenceByCid.end() ||
		it_failed == m_FailByCid.end())
	{
		// error
		return false;
	}

	LOGD << std::format("NSStorageManager::SetUpdatedSequence() cid: {}, Pending: {}, CurrentSeq: {}, TargetSeq: {}",
		cid, it_pending->second, it_updated->second, updatedSeq);

	Touch(cid);
	if (it_updated->second + 1 != updatedSeq)
	{
		LOGE << std::format("NSStorageManager::SetUpdatedSequence() cid: {} mismatch sequence.", cid);
		return false;
	}

	it_updated->second = updatedSeq;
	it_failed->second = 0;

	LOGD << std::format("NSStorageManager::SetUpdatedSequence() cid: {}, Pending: {}, CurrentSeq: {}, TargetSeq: {}",
		cid, it_pending->second, it_updated->second, updatedSeq);

	return true;
}

void NSStorageManager::LoadUpdatedSequence(int64_t cid, int64_t updatedSeq)
{
	//auto it_pending = m_PendingSequenceByCid.find(cid);
	//if (it_pending != m_PendingSequenceByCid.end())
	//{
	//	if (!it_pending->second.empty())
	//	{
	//		LOGW << std::format("NSStorageManager::LoadUpdatedSequence() pending update exists. CID: {}", cid);
	//		return;
	//	}
	//}

#ifdef _DEBUG
	LOGD << std::format("NSStorageManager::LoadUpdatedSequence() Cid: {}, DBSequence: {}", cid, updatedSeq);
#endif

	Touch(cid);
	{
		auto result = m_PendingSequenceByCid.emplace(cid, 0);
		result.first->second = updatedSeq;
	}

	{
		auto result = m_UpdatedSequenceByCid.emplace(cid, 0);
		result.first->second = updatedSeq;
	}

	{
		auto result = m_FailByCid.emplace(cid, 0);
		result.first->second = 0;
	}
}

void NSStorageManager::SubtractPendingSequence(int64_t cid, size_t SubtractCount)
{
	auto it_pending = m_PendingSequenceByCid.find(cid);	

	if ( it_pending != m_PendingSequenceByCid.end()) 
		it_pending->second -= SubtractCount;
}

const int32_t NSStorageManager::GetFailCount(int64_t cid) const
{
	auto it = m_FailByCid.find(cid);
	return it == m_FailByCid.end()
		? 0
		: it->second;
}

void NSStorageManager::AddFailCount(int64_t cid)
{
	auto result = m_FailByCid.emplace(cid, 0);
	++result.first->second;
}

void NSStorageManager::RemoveData(int64_t cid)
{
	LOGI << std::format("NSStorageManager::RemoveData() Removing sequence data. CID: {}", cid);

	m_PendingSequenceByCid.erase(cid);
	m_UpdatedSequenceByCid.erase(cid);
	m_FailByCid.erase(cid);
}

void NSStorageManager::Touch(int64_t cid)
{
	uint64_t expires = NSTickManager::GetInstance()->GetUnixTimeStamp() + CONTAINER_TTL_MS;
	auto result = m_ExpiredByCid.emplace(cid, expires);
	if (!result.second)
	{
		result.first->second = expires;
	}
}