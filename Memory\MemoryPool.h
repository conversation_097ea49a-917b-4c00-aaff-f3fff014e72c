#pragma once
#include <memory>
#include <queue>
#include <mutex>
#include <atomic>

// 심플한 메모리 풀 (객체 재사용)
template<typename T>
class MemoryPool
{
public:
    using Deleter = std::function<void(T*)>;
    using UniquePtr = std::unique_ptr<T, Deleter>;

    MemoryPool(size_t initialSize = 16, size_t maxSize = 1024)
        : m_maxSize(maxSize)
    {
        // 초기 객체 생성
        for (size_t i = 0; i < initialSize; ++i)
        {
            m_pool.push(std::make_unique<T>());
        }
        m_currentSize = initialSize;
    }

    ~MemoryPool() = default;

    // 객체 획득
    UniquePtr Acquire()
    {
        std::unique_lock<std::mutex> lock(m_mutex);
        
        T* obj = nullptr;
        
        if (!m_pool.empty())
        {
            obj = m_pool.front().release();
            m_pool.pop();
        }
        else if (m_currentSize < m_maxSize)
        {
            obj = new T();
            m_currentSize++;
        }
        else
        {
            // 풀이 가득 찬 경우 대기
            m_cv.wait(lock, [this] { return !m_pool.empty(); });
            obj = m_pool.front().release();
            m_pool.pop();
        }

        // 커스텀 삭제자로 unique_ptr 생성
        return UniquePtr(obj, [this](T* ptr) {
            Return(ptr);
        });
    }

    // 통계
    size_t GetPoolSize() const 
    { 
        std::lock_guard<std::mutex> lock(m_mutex);
        return m_pool.size(); 
    }
    
    size_t GetCurrentSize() const { return m_currentSize; }

private:
    // 객체 반환
    void Return(T* obj)
    {
        if (!obj) return;

        std::lock_guard<std::mutex> lock(m_mutex);
        
        // 객체 초기화
        if constexpr (std::is_member_function_pointer_v<decltype(&T::Reset)>)
        {
            obj->Reset();
        }
        
        m_pool.push(std::unique_ptr<T>(obj));
        m_cv.notify_one();
    }

private:
    mutable std::mutex m_mutex;
    std::condition_variable m_cv;
    std::queue<std::unique_ptr<T>> m_pool;
    std::atomic<size_t> m_currentSize{0};
    size_t m_maxSize;
};