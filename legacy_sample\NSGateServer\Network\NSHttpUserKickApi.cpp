#include "stdafx.h"
#include "NSHttpUserKickApi.h"
#include "NSClientSessionManager.h"

void NSHttpUserKickApi::Execute()
{
	m_ExcuteReturn = OnExecute();
	Complete();
}

bool NSHttpUserKickApi::OnExecute()
{
	int64_t accountID = GetValue("aid", static_cast<int64_t>(0));

	const std::shared_ptr<NSClientSession> clientSession = NSClientSessionManager::GetInstance()->GetSessionByAID(accountID);
	if (clientSession == nullptr)
	{
		m_Result["result"] = "fail";
		m_Result["reason"] = "not found";
		return false;
	}
	const int64_t socketChannelID = clientSession->GetSocketChannelId();
	NSClientSessionManager::GetInstance()->CloseSession(socketChannelID);

	m_Result["result"] = "success";

	return true;
}
