// Storage Update 시스템 테스트 코드
#include "NSDataBaseManager.h"
#include "Storage/NSStorageUpdater.h"
#include "Storage/NSStorageModel.h"
#include "NSDBSession.h"
#include <iostream>
#include <thread>

// 테스트용 모델 클래스
class TestCharacterModel : public NSStorageModel
{
public:
    static constexpr int32_t TypeId = 1001;
    static constexpr const char* strComponentName = "TestCharacter";
    static constexpr const char* spSelect = "spSelectCharacter";
    static constexpr const char* spUpsert = "spUpsertCharacter";
    
    DECLARE_MODEL_UTIL
    
    void Serialize(OUT std::vector<std::string>& payloads) override {
        payloads.push_back("{\"level\":50,\"exp\":10000}");
    }
    
    void SerializeAffected(OUT std::vector<std::string>& payloads) override {
        if (IsDirty()) {
            Serialize(payloads);
        }
    }
    
    void Rollback() override {
        SetDirty(false);
    }
    
    static int32_t GetTypeId() { return TypeId; }
};

// 테스트용 Escrow 모델 클래스
class TestItemModel : public NSStorageEscrowModel
{
public:
    static constexpr int32_t TypeId = 1002;
    static constexpr const char* strComponentName = "TestItem";
    static constexpr const char* spSelect = "spSelectItem";
    static constexpr const char* spUpsert = "spUpsertItem";
    static constexpr const char* spDeposit = "spDepositItem";
    static constexpr const char* spWithdraw = "spWithdrawItem";
    static constexpr const char* spReclaim = "spReclaimItem";
    
    DECLARE_MODEL_UTIL
    DECLARE_ESCROW_UTIL
    
    void Serialize(OUT std::vector<std::string>& payloads) override {
        payloads.push_back("{\"itemId\":1001,\"count\":5}");
    }
    
    void SerializeAffected(OUT std::vector<std::string>& payloads) override {
        if (IsDirty()) {
            Serialize(payloads);
        }
    }
    
    void SerializeEscrow(OUT std::string& payload, const int64_t receiverCid) override {
        payload = "{\"receiverCid\":" + std::to_string(receiverCid) + ",\"itemId\":1001,\"count\":5}";
    }
    
    void Rollback() override {
        SetDirty(false);
    }
    
    static int32_t GetTypeId() { return TypeId; }
};

void TestStorageUpdate()
{
    std::cout << "=== Storage Update System Test ===" << std::endl;
    
    // 1. 시스템 초기화
    NSDataBaseManager::GetInstance()->Initialize();
    NSDataBaseManager::GetInstance()->Start(8); // 8개 워커 스레드
    
    // 연결 정보 추가 (예제)
    NSDataBaseManager::GetInstance()->AddConnectionInfo(0, 0, "localhost", 3306, "gamedb_0", "user", "password");
    
    // 2. 세션 생성 (테스트용)
    auto session = std::make_shared<NSDBSession>();
    session->SetAID(12345);
    
    int64_t aid = 12345;
    int64_t cid = 67890;
    
    // 3. 세션 시작 시 DB에서 시퀀스를 읽어와서 설정하는 과정이 필요 (생략)
    
    // 4. Storage Updater 생성 및 모델 추가
    {
        NSStorageUpdater updater(session, aid, cid);
        
        // 캐릭터 모델 추가
        TestCharacterModel characterModel;
        characterModel.SetAid(aid);
        characterModel.SetCid(cid);
        characterModel.SetDirty(true);
        updater.AddModelToUpdate(&characterModel);
        
        // 아이템 모델 추가
        TestItemModel itemModel;
        itemModel.SetAid(aid);
        itemModel.SetCid(cid);
        itemModel.SetDirty(true);
        updater.AddModelToUpdate(&itemModel);
        
        // 커밋 (비동기 처리)
        updater.Commit([](const std::shared_ptr<NSQueryData>& queryData, const NSStorageUpdateContainer* container) {
            std::cout << "Update completed for Model #" << container->ModelId 
                     << " Seq: " << container->Seq << std::endl;
        });
    }
    
    // 5. Escrow 테스트
    {
        NSStorageUpdater updater(session, aid, cid);
        
        TestItemModel itemModel;
        itemModel.SetAid(aid);
        itemModel.SetCid(cid);
        itemModel.SetDirty(true);
        updater.AddModelToUpdate(&itemModel);
        
        // Escrow Deposit
        int64_t receiverCid = 99999;
        updater.CommitWithEscrowDeposit(receiverCid, [](const std::shared_ptr<NSQueryData>& queryData, const NSStorageUpdateContainer* container) {
            std::cout << "Escrow deposit completed. TransactionId: " << container->EscrowTransactionId << std::endl;
        });
    }
    
    // 6. 통계 출력
    std::this_thread::sleep_for(std::chrono::seconds(2)); // 비동기 처리 대기
    
    std::cout << "\n=== Statistics ===" << std::endl;
    std::cout << "Processing queries: " << NSDataBaseManager::GetInstance()->GetQueriesProcessingCount() << std::endl;
    std::cout << "DB Queue size: " << NSDataBaseManager::GetInstance()->GetDBQueueSize() << std::endl;
    std::cout << "Input count: " << NSDataBaseManager::GetInstance()->GetInputCount() << std::endl;
    std::cout << "Output count: " << NSDataBaseManager::GetInstance()->GetOutputCount() << std::endl;
    std::cout << "Connection pool info: " << NSDataBaseManager::GetInstance()->GetConnectionPoolCountInfo() << std::endl;
    
    // 7. 세션 종료 시 데이터 정리
    NSDataBaseManager::GetInstance()->OnSessionClosed(cid);
    
    // 8. 시스템 종료
    NSDataBaseManager::GetInstance()->Stop();
    NSDataBaseManager::GetInstance()->Finalize();
    
    std::cout << "\n=== Test Completed ===" << std::endl;
}

int main()
{
    try {
        TestStorageUpdate();
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
    }
    
    return 0;
}