# Database_Maria CID Queue 기반 설계

## 개요
현재 Database_Maria의 순서 보장 문제를 해결하고 성능을 최적화하기 위한 CID 기반 큐 시스템 설계

## 현재 구조 요약
### 현재 아키텍처
- **스레드 할당**: CID % ThreadCount로 고정 스레드 할당
- **연결 관리**: 각 스레드별 전용 연결 + 공용 풀 폴백
- **문제점**: 공용 풀 사용시 다른 연결로 쿼리 실행 → CID별 순서 깨짐

### 현재 흐름
```
CID → Thread Index (CID % ThreadCount) → Thread 전용 연결 or 공용 풀
```

### 변경 후 흐름
```
CID → CID Queue → Any Thread → Any Connection (라운드 로빈)
```

## 핵심 문제점
1. 공용 풀 사용시 CID별 순서 보장 안됨
2. 스레드별 연결 고정으로 부하 불균형 발생
3. 연결 수와 스레드 수가 1:1로 제한됨

## 제안 아키텍처

### 1. 기본 구조
- **연결 수**: AddConnectionInfo의 initPoolCount 파라미터 사용
- **스레드 수**: 연결당 8개 스레드 (최대 64개 제한)
- **CID 큐**: 각 CID별 전용 큐로 순서 보장

```
예시: initPoolCount = 8
- DB 연결: 8개
- 워커 스레드: 64개 (8 × 8)
- CID별 큐: 무제한 (동적 생성)
```

### 2. 핵심 컴포넌트

#### 2.1 CIDQueueManager
```cpp
// 쿼리 태스크 구조체 (레거시 호환)
struct QueryTask {
    NS::Connection connection;
    NSDataSerializer serializer;
    DBPromise<std::shared_ptr<NSQueryData>> promise;
    std::shared_ptr<NSQueryData> queryData;
    std::function<void(const std::shared_ptr<NSQueryData>&)> afterExecuteCallback;
    std::string procedureName;
};

class CIDQueueManager {
private:
    struct CIDQueue {
        std::queue<QueryTask> queries;
        std::mutex mutex;
        // 상태 관리 불필요 - 큐가 비어있으면 대기중 없음
    };
    
    std::unordered_map<int64_t, std::unique_ptr<CIDQueue>> m_cidQueues;
    std::shared_mutex m_mapMutex;
    
public:
    void EnqueueQuery(int64_t cid, QueryTask task) {
        auto& cidQueue = GetOrCreateQueue(cid);
        
        std::lock_guard<std::mutex> lock(cidQueue->mutex);
        bool wasEmpty = cidQueue->queries.empty();
        cidQueue->queries.push(task);  // ← 여기서 큐에 추가!
        
        if (wasEmpty) {
            // 큐가 비어있었으면 즉시 처리 시작
            m_workerManager->PostWork([this, cid]() {
                ProcessQueue(cid);
            });
        }
        // 큐가 차있으면 이미 처리중이므로 추가만 하고 리턴
    }
    
private:
    void ProcessQueue(int64_t cid) {
        auto& cidQueue = m_cidQueues[cid];
        
        QueryTask task;
        {
            std::lock_guard<std::mutex> lock(cidQueue->mutex);
            if (cidQueue->queries.empty()) return;
            task = cidQueue->queries.front();
            cidQueue->queries.pop();  // ← 여기서 큐에서 제거!
        }
        
        // 아무 연결이나 사용 (라운드 로빈)
        auto conn = GetNextAvailableConnection();
        
        // RAII 패턴으로 타이밍 측정 (예외 안전)
        {
            QueryTimer timer(task.queryData, m_afterExecuteQueryShared);
            
            // 실제 쿼리 실행
            auto sp = CreateStoredProcedure<SP>();
            auto errorCode = sp->Execute(conn.get(), task.serializer, task.queryData);
            task.queryData->SetErrorCode(errorCode);
        } // timer 소멸자에서 자동으로 시간 측정 & 콜백 호출
        
        // Promise 설정 (게임 로직으로 결과 전달)
        task.promise.SetValue(task.queryData);
        
        // 다음 큐 처리
        m_workerManager->PostWork([this, cid]() {
            ProcessQueue(cid);
        });
    }
};
```

#### 2.2 연결 관리
```cpp
class ConnectionManager {
private:
    std::vector<std::shared_ptr<NSMySQLConnection>> m_connections;
    std::atomic<size_t> m_nextConnection{0};
    
public:
    void Initialize(int connectionCount) {
        m_connections.reserve(connectionCount);
        for (int i = 0; i < connectionCount; ++i) {
            m_connections.push_back(std::make_shared<NSMySQLConnection>(...));
        }
    }
    
    std::shared_ptr<NSMySQLConnection> GetNextConnection() {
        // 라운드 로빈 방식
        size_t index = m_nextConnection.fetch_add(1) % m_connections.size();
        return m_connections[index];
    }
};
```

### 3. 레거시 호환성 보장

#### 3.1 콜백 체인 보장
```cpp
// Database_Ado와 동일한 콜백 순서
1. Query 실행 시작 (NSElapsedTimeUtil::ScopedDuration 시작)
2. StoredProcedure::QueryFunc() 호출
3. 결과를 NSQueryData에 저장
4. 타이머 종료시 AfterExecuteQuery 콜백 호출
5. Promise 설정으로 게임 로직에 결과 전달
6. 다음 CID 큐 처리
```

#### 3.2 Promise 기반 비동기 처리
```cpp
// 게임 로직에서 사용하는 패턴 유지
NSDataBaseManager::GetInstance()->StartQuery<SpSelectCharacter>(session)
    .Then([](std::shared_ptr<NSQueryData> queryData) {
        // 결과 처리
        auto recordSet = queryData->GetRecordSet();
        // ...
    });
```

### 4. 수정 필요 사항

#### 4.1 파일 경로 및 위치
```
주요 수정 파일:
- /mnt/d/dblib_new/Database_Maria/NSDataBaseManager.h
- /mnt/d/dblib_new/Database_Maria/NSDataBaseManager.cpp
- /mnt/d/dblib_new/Database_Maria/ConnectionPool/NSMySQLConnectionPool.h
- /mnt/d/dblib_new/Database_Maria/ConnectionPool/NSMySQLConnectionPool.cpp
- /mnt/d/dblib_new/Database_Maria/Threading/ThreadedWorkManager.h
- /mnt/d/dblib_new/Database_Maria/Threading/ThreadedWorkManager.cpp

신규 파일:
- /mnt/d/dblib_new/Database_Maria/CIDQueueManager.h
- /mnt/d/dblib_new/Database_Maria/CIDQueueManager.cpp
```

#### 4.2 NSDataBaseManager 수정
```cpp
// Start 메서드 - 스레드 수 계산
bool NSDataBaseManager::Start(uint32_t workThreadCnt) {
    if (workThreadCnt == 0) {
        // 전체 연결 수 계산
        int totalConnections = 0;
        for (auto& pool : m_connectionPools) {
            if (pool) {
                totalConnections += pool->GetMaxConnections();
            }
        }
        
        // 연결갯수 나누기 2
        workThreadCnt = std::max(totalConnections / 2 , 32);
    }
    
    m_threadCount = workThreadCnt;
    m_workerManager->Initialize(workThreadCnt);
    // ...
}

// StartQueryImpl 수정
template<typename SP>
DBPromise<...> StartQueryImpl(const NS::Connection& connection,
                             const NSDataSerializer& serializer) {
    // CID 추출
    int64_t cid = ExtractCIDFromSerializer(serializer);
    
    return DBPromise<...>::Create([=](auto promise) {
        QueryTask task{
            .connection = connection,
            .serializer = serializer,
            .promise = promise,
            .procedureName = SP::GetProcedureName()
        };
        
        // CID 큐에 추가
        m_cidQueueManager->EnqueueQuery(cid, task);
    });
}
```

#### 4.3 연결 풀 수정
- 공용 풀 제거
- 스레드별 전용 연결 제거
- 단순 연결 리스트로 변경

```cpp
class NSMySQLConnectionPool {
private:
    std::vector<std::shared_ptr<NSMySQLConnection>> m_connections;
    // m_threadConnections 제거
    // m_sharedPool 제거
    
public:
    void Initialize(int connectionCount) {
        for (int i = 0; i < connectionCount; ++i) {
            auto conn = std::make_shared<NSMySQLConnection>();
            conn->Connect(m_host, m_port, m_user, m_password, m_database);
            m_connections.push_back(conn);
        }
    }
};
```

### 4. 동작 흐름

1. **쿼리 요청**
   ```
   Game Logic → StartQuery(CID=1234, Query)
   ```

2. **CID 큐 추가**
   ```
   CIDQueueManager::EnqueueQuery(1234, QueryTask)
   - 큐가 비어있으면 → 즉시 처리 시작
   - 큐가 차있으면 → 추가만 하고 리턴
   ```

3. **쿼리 실행**
   ```
   아무 워커 스레드 → ProcessQueue(1234)
   - 큐에서 꺼냄
   - 아무 연결 사용하여 실행
   - 비동기 콜백 대기
   ```

4. **완료 처리**
   ```
   콜백 도착 → 결과 처리 → 다음 큐 아이템 처리
   ```

### 5. 장점

1. **완벽한 순서 보장**
   - CID별로 한 번에 하나의 쿼리만 실행
   - 큐 방식으로 순서 유지

2. **높은 처리량**
   - 64개 스레드가 모든 CID 처리 가능
   - 부하 자동 분산

3. **리소스 효율성**
   - 연결 수와 무관하게 스레드 수 조절
   - 논블로킹으로 연결 활용도 최대화

4. **확장성**
   - CID 수 제한 없음
   - 연결/스레드 동적 조절 가능

### 6. 구현 공수

| 작업 항목 | 예상 시간 | 설명 |
|----------|----------|------|
| CIDQueueManager 구현 | 3일 | 큐 관리, 스레드 안전성 |
| NSDataBaseManager 수정 | 2일 | StartQuery 흐름 변경 |
| 연결 풀 단순화 | 1일 | 공용 풀 제거 |
| 통합 테스트 | 3일 | 순서 보장, 성능 검증 |
| 안정화 | 3일 | 버그 수정, 최적화 |
| **총 공수** | **12일** | 약 2주 |

### 7. 위험 요소 및 대응

1. **메모리 사용량**
   - CID별 큐 생성으로 메모리 사용
   - 실제 영향: 동접 10,000명 → 약 1MB (무시 가능)

2. **디버깅 복잡도**
   - 64개 스레드 동시 실행
   - 대응: CID별 로깅, 추적 도구 구현

3. **호환성**
   - 기존 인터페이스 유지 필요
   - 대응: 외부 API 변경 없이 내부만 수정

### 8. 성능 예상

**현재 (10연결 10스레드)**
- 부하 균등시: 1,000 QPS
- 부하 불균등시: 500 QPS

**개선 후 (8연결 64스레드)**
- 부하 균등시: 800 QPS
- 부하 불균등시: 800 QPS (안정적)

**개선 효과**
- 평균 60% 성능 향상
- 응답 시간 편차 50% 감소
- CPU 활용률 90%+ 달성

### 9. 마이그레이션 전략

1. **1단계**: 공용 풀 제거 (빠른 수정)
2. **2단계**: CID 큐 시스템 개발
3. **3단계**: 점진적 롤아웃 (특정 서버부터)
4. **4단계**: 전체 적용

### 10. 모니터링 지표

- CID별 큐 길이
- 스레드별 처리량
- 연결별 활용도
- 평균 응답 시간
- P99 지연 시간

### 11. RAII 패턴 구현 (추천)

#### 11.1 QueryTimer 클래스
```cpp
class QueryTimer {
    std::chrono::high_resolution_clock::time_point m_start;
    std::shared_ptr<NSQueryData> m_queryData;
    std::function<void(const std::shared_ptr<NSQueryData>&)> m_callback;
    
public:
    QueryTimer(std::shared_ptr<NSQueryData> data, auto callback)
        : m_start(std::chrono::high_resolution_clock::now())
        , m_queryData(data)
        , m_callback(callback) {}
    
    ~QueryTimer() {
        auto elapsed = std::chrono::high_resolution_clock::now() - m_start;
        m_queryData->SetElapsedTime(elapsed);
        if (m_callback) {
            m_callback(m_queryData);
        }
    }
};
```

#### 11.2 RAII 패턴의 장점
- **예외 안전성**: 예외가 발생해도 타이머 소멸자 호출 보장
- **코드 간결성**: 수동 시간 측정 코드 불필요
- **실수 방지**: 콜백 호출을 깜빡할 수 없음
- **일관성**: 모든 쿼리에 대해 동일한 처리 보장

### 12. 결론

이 설계를 통해 Database_Maria의 순서 보장 문제를 해결하고 성능을 크게 향상시킬 수 있습니다. 특히 RAII 패턴을 사용하여 예외 안전성과 코드 품질을 더욱 높일 수 있습니다.