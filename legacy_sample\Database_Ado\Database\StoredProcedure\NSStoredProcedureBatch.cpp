#include "stdafx.h"
#include "NSStoredProcedureBatch.h"
#include "DataBase/NSDataBaseManager.h"
#include "DataBase/StoredProcedure/NSStoredProcedure.h"

void NSStoredProcedureBatch::AddStoredProcedure(NSStoredProcedure* procedure)
{
	if (m_vecProcedures.size() >= g_uMaxQueryRecordSetCount)
	{
		LOGE << "Stored batch exceed the limit count. Check g_uMaxQueryRecordSetCount.";
		return;
	}

	m_vecProcedures.push_back(procedure);
}

EErrorCode NSStoredProcedureBatch::QueryFunc(const std::shared_ptr<NSQueryData> queryData)
{
	EErrorCode errorCode = EErrorCode::None;

	for (auto spProcedure : m_vecProcedures)
	{
		errorCode = spProcedure->QueryFunc(queryData);
		if (errorCode != EErrorCode::None)
			return errorCode;
	}

	return EErrorCode::None;
}
