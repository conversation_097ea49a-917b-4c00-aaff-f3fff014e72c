#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpCheckCharacterName : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spCheckCharacterName";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);
	EErrorCode MakeOutput(NSAdoCommand* command);

public:
	struct Input
	{
		char Name[g_uMaxCharacterNameUTF8Length];
		int32_t Wid;
	} Input;

	struct Output
	{
		int32_t Result{ 0 };
	} Output;

	SpCheckCharacterName() = default;
	SpCheckCharacterName(const char* name, const int32_t wid);
};
