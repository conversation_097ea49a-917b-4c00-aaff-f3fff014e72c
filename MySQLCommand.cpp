#include "stdafx.h"
#include "MySQLCommand.h"

MySQLCommand::MySQLCommand()
{
}

MySQLCommand::~MySQLCommand()
{
}

bool MySQLCommand::GetItem(const std::string& name, int32_t& value)
{
    auto it = m_outputParameters.find(name);
    if (it == m_outputParameters.end())
        return false;
    
    if (auto* v = std::get_if<int32_t>(&it->second))
    {
        value = *v;
        return true;
    }
    return false;
}

bool MySQLCommand::GetItem(const std::string& name, int64_t& value)
{
    auto it = m_outputParameters.find(name);
    if (it == m_outputParameters.end())
        return false;
    
    if (auto* v = std::get_if<int64_t>(&it->second))
    {
        value = *v;
        return true;
    }
    return false;
}

bool MySQLCommand::GetItem(const std::string& name, float& value)
{
    auto it = m_outputParameters.find(name);
    if (it == m_outputParameters.end())
        return false;
    
    if (auto* v = std::get_if<float>(&it->second))
    {
        value = *v;
        return true;
    }
    return false;
}

bool MySQLCommand::GetItem(const std::string& name, double& value)
{
    auto it = m_outputParameters.find(name);
    if (it == m_outputParameters.end())
        return false;
    
    if (auto* v = std::get_if<double>(&it->second))
    {
        value = *v;
        return true;
    }
    return false;
}

bool MySQLCommand::GetItem(const std::string& name, std::string& value)
{
    auto it = m_outputParameters.find(name);
    if (it == m_outputParameters.end())
        return false;
    
    if (auto* v = std::get_if<std::string>(&it->second))
    {
        value = *v;
        return true;
    }
    return false;
}

bool MySQLCommand::GetItem(const std::string& name, std::vector<uint8_t>& value)
{
    auto it = m_outputParameters.find(name);
    if (it == m_outputParameters.end())
        return false;
    
    if (auto* v = std::get_if<std::vector<uint8_t>>(&it->second))
    {
        value = *v;
        return true;
    }
    return false;
}

void MySQLCommand::BindData::PrepareBinding()
{
    std::visit([this](auto&& arg) {
        using T = std::decay_t<decltype(arg)>;
        
        if constexpr (std::is_same_v<T, int32_t>)
        {
            bind.buffer_type = MYSQL_TYPE_LONG;
            bind.buffer = &std::get<int32_t>(data);
            bind.is_unsigned = 0;
        }
        else if constexpr (std::is_same_v<T, int64_t>)
        {
            bind.buffer_type = MYSQL_TYPE_LONGLONG;
            bind.buffer = &std::get<int64_t>(data);
            bind.is_unsigned = 0;
        }
        else if constexpr (std::is_same_v<T, float>)
        {
            bind.buffer_type = MYSQL_TYPE_FLOAT;
            bind.buffer = &std::get<float>(data);
        }
        else if constexpr (std::is_same_v<T, double>)
        {
            bind.buffer_type = MYSQL_TYPE_DOUBLE;
            bind.buffer = &std::get<double>(data);
        }
        else if constexpr (std::is_same_v<T, std::string>)
        {
            const auto& str = std::get<std::string>(data);
            buffer.assign(str.begin(), str.end());
            bind.buffer_type = MYSQL_TYPE_STRING;
            bind.buffer = buffer.data();
            bind.buffer_length = buffer.size();
            length = buffer.size();
            bind.length = &length;
        }
        else if constexpr (std::is_same_v<T, std::vector<uint8_t>>)
        {
            const auto& vec = std::get<std::vector<uint8_t>>(data);
            buffer = vec;
            bind.buffer_type = MYSQL_TYPE_BLOB;
            bind.buffer = buffer.data();
            bind.buffer_length = buffer.size();
            length = buffer.size();
            bind.length = &length;
        }
        
        bind.is_null = &is_null;
    }, data);
}

void MySQLCommand::BindToMySQL(MYSQL_STMT* stmt, std::vector<MYSQL_BIND>& binds)
{
    // 바인드 데이터 준비
    m_bindData.clear();
    m_bindData.reserve(m_parameters.size());
    
    // 파라미터를 바인드 데이터로 변환
    for (const auto& [name, value] : m_parameters)
    {
        BindData bd;
        bd.data = value;
        bd.PrepareBinding();
        m_bindData.push_back(std::move(bd));
    }
    
    // MySQL에 전달할 MYSQL_BIND 배열 준비
    binds.clear();
    binds.reserve(m_bindData.size());
    
    for (auto& bd : m_bindData)
    {
        binds.push_back(bd.bind);
    }
    
    if (!binds.empty())
    {
        mysql_stmt_bind_param(stmt, binds.data());
    }
}

void MySQLCommand::RetrieveOutputParameters(MYSQL_STMT* stmt)
{
    // OUT 파라미터 가져오기
    // 실제 구현에서는 프로시저 메타데이터를 참조하여
    // OUT/INOUT 파라미터만 가져와야 함
    
    // 여기서는 간단히 구현
    m_outputParameters = m_parameters; // 임시로 복사
}