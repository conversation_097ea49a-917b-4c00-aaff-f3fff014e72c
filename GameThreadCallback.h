#pragma once
#include <functional>
#include <windows.h>

// 게임 스레드 콜백 시스템
class GameThreadCallback
{
public:
    using PostMessageFunc = std::function<void(void* executor, std::function<void()> task)>;
    
    // 게임 스레드 포스트 함수 설정
    static void SetPostMessageFunction(PostMessageFunc func)
    {
        s_postMessageFunc = func;
    }
    
    // 게임 스레드로 작업 포스트
    static void PostToGameThread(void* executor, std::function<void()> task)
    {
        if (s_postMessageFunc)
        {
            s_postMessageFunc(executor, std::move(task));
        }
        else
        {
            // 포스트 함수가 설정되지 않은 경우 현재 스레드에서 실행
            task();
        }
    }
    
    // Windows 메시지 기반 구현 예시
    static void PostViaWindowsMessage(HWND hwnd, UINT msg, std::function<void()> task)
    {
        auto* taskPtr = new std::function<void()>(std::move(task));
        PostMessage(hwnd, msg, reinterpret_cast<WPARAM>(taskPtr), 0);
    }

private:
    static inline PostMessageFunc s_postMessageFunc = nullptr;
};

// DBPromise에서 사용할 전역 함수
template<typename T>
void DBPromise<T>::PostToGameThread(void* executor, std::function<void()> task)
{
    GameThreadCallback::PostToGameThread(executor, std::move(task));
}