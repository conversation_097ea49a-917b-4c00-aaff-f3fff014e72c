#pragma once

#include <string>
#include <cstdint>

namespace Database
{

struct ConnectionInfo
{
    std::string host;
    uint16_t port = 3306;
    std::string user;
    std::string password;
    std::string database;
    std::string charset = "utf8mb4";
    uint32_t connectionFlags = 0;
    
    ConnectionInfo() = default;
    
    ConnectionInfo(const std::string& h, uint16_t p, const std::string& u, 
                  const std::string& pw, const std::string& db, 
                  const std::string& cs = "utf8mb4", uint32_t flags = 0)
        : host(h), port(p), user(u), password(pw), database(db), charset(cs), connectionFlags(flags)
    {}
};

} // namespace Database