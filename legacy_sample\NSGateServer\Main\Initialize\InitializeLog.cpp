#include "stdafx.h"

#include "ServerMain/NSServerMain.h"
#include "Logger/NSLoggerBuidler.h"
#include "Logger/NSNanosecondsSinceEpochFlag.h"
#include "Logger/NSTimezoneFlag.h"
#include "Logger/NSEnvFlag.h"
#include "Logger/NSUserStringFlag.h"

#include "Version/NSProjectVersion.h"

void InitializeLog(const EServerType serverType)
{
	std::string serverName = to_string(serverType);
	const NSLogLevel logSeverity = System::GetInstance()->GetLogLevel();
	const uint32_t maxLogFileSize = System::GetInstance()->GetMaxLogFileSize();

	if (System::GetInstance()->IsSystemLogUsed())
	{
		NSLoggerBuilder builder(logSeverity, "FileLogger");
		builder.AddPatternflag<NSTimezoneFlag>();
		builder.AddPatternflag<NSUserStringFlag>(std::format("[{}] [{}]", NSUtil::GetLocalIP(), serverName));
		builder.SetPattern(System::GetInstance()->GetSystemLogPattern());
		builder.ConfigureFile(System::GetInstance()->GetSystemLogFilePath(), maxLogFileSize);

		NSLogManager::GetInstance().SetFileLogger(builder);
	}

	if (System::GetInstance()->IsKGPerformanceLogUsed())
	{
		NSLoggerBuilder builder(logSeverity, "KGPerformanceLogger");
		builder.SetPattern(System::GetInstance()->GetKGPerformanceLogPattern());
		builder.ConfigureFile(System::GetInstance()->GetKGPerformanceLogFilePath(), maxLogFileSize);
		builder.AddPatternflag<NSTimezoneFlag>();
		builder.AddPatternflag<NSUserStringFlag>(std::format("[{}] [{}]", NSUtil::GetLocalIP(), serverName));

		NSLogManager::GetInstance().AddUserLogger(LOG_ID_KG_PERFORMANCE, builder);
	}

	if (System::GetInstance()->IsKGDBQueryExecutionLogUsed())
	{
		NSLoggerBuilder builder(logSeverity, "KGDBQueryExecutionLogger");
		builder.SetPattern(System::GetInstance()->GetKGDBQueryExecutionLogPattern());
		builder.ConfigureFile(System::GetInstance()->GetKGDBQueryExecutionLogFilePath(), maxLogFileSize);
		builder.AddPatternflag<NSTimezoneFlag>();
		builder.AddPatternflag<NSUserStringFlag>(std::format("[{}] [{}]", NSUtil::GetLocalIP(), serverName));

		NSLogManager::GetInstance().AddUserLogger(LOG_ID_KG_DB_QUERY_EXECUTION, builder);
	}

	if (System::GetInstance()->IsKGLogUsed())
	{
		NSLoggerBuilder builder(logSeverity, "KGLogger");
		builder.SetPattern(System::GetInstance()->GetKGLogPattern());
		builder.ConfigureFile(System::GetInstance()->GetKGLogFilePath(), maxLogFileSize);

		NSLogManager::GetInstance().AddUserLogger(LOG_ID_KG_ACTION, builder);
	}
}
void AppendWindowConsoleLog()
{
	NSLogLevel logSeverity = System::GetInstance()->GetLogLevel();

	if (System::GetInstance()->IsConsoleLogUsed() && (g_pcServerMain && g_pcServerMain->GetServerServiceType() == EServerServiceType::Console))
	{
		NSLoggerBuilder builder(logSeverity, "ConsoleLogger");
		builder.SetPattern(System::GetInstance()->GetConsoleLogPattern());

#ifdef _DEBUG
		builder.ConfigureConsole(false);
#else
		builder.ConfigureConsole(true);
#endif

		NSLogManager::GetInstance().SetConsoleLogger(builder);
	}
}
