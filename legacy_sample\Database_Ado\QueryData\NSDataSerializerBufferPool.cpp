#include "stdafx.h"
#include "NSDataSerializerBufferPool.h"

NSDataSerializerBufferBase::NSDataSerializerBufferBase(NSDataSerializerBufferBase&& rhs) noexcept
	: m_Buffer(std::move(rhs.m_Buffer))
	, m_MaxSize(rhs.m_MaxSize)
	, m_Size(rhs.m_Size)
{
	rhs.m_MaxSize = -1;
	rhs.m_Size = -1;
}

NSDataSerializerBufferBase& NSDataSerializerBufferBase::operator=(NSDataSerializerBufferBase&& rhs) noexcept
{
	m_Buffer = std::move(rhs.m_Buffer);
	m_MaxSize = rhs.m_MaxSize;
	m_Size = rhs.m_Size;

	rhs.m_MaxSize = -1;
	rhs.m_Size = -1;

	return *this;
}

NSDataSerializerBufferBase::NSDataSerializerBufferBase(int32_t iSize)
	: m_Buffer(std::unique_ptr<char[]>(new char[iSize]))
	, m_MaxSize(iSize)
	, m_Size(0)
{
}

NSDataSerializerBufferBase::~NSDataSerializerBufferBase()
{
}
