#include "stdafx.h"
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif

#pragma comment(lib, "winmm")

#include "ServerMain/NSServerMain.h"

#include "Network/NSWorldServerHandler.h"
#include "Network/NSClientHandler.h"
#include "Network/NSCommunityServerHandler.h"
#include "Http/NSHttpListener.h"
#include "GameServer/NSWorldServerManager.h"
#include "NSRandom.h"
#include "Logger/NSLoggerBuidler.h"
#include "Packet/NSPacketPool.h"
#include "NSTickManager/NSTickManager.h"
#include "DataBase/NSDataBaseManager.h"
#include "NSGateLogicThread.h"
#include "Version/NSProjectVersion.h"
#include "Data/Manager/NSDataLoader.h"

#include "Data/NSCharacterTemplate.h"
#include "Data/NSWorldTemplate.h"
#include "Data/NSWorldSpawnTemplate.h"
#include "Data/NSGameConfigTemplate.h"
#include "Data/NSBannedWordTemplate.h"
#include "Data/NSTextTemplate.h"
#include "Data/NSCostumeTemplate.h"

#include "Util/NpLib_TableEnumUtil.h"
#include <execution>

#include "Guid/NSGuidManager.h"

#include <DataBase/Query/NSQuery.h>
#include <DataBase/Query/NSQrGetDBTime.h>

#include "CNLServiceLogicAdapter.h"
#include "NSUtil/NSRedisUtil.h"
#include "NSUtil/NSDumpAgentUtil.h"

#include "DataBase/NSConnectionPool.h"
#include "Network/NSXignCodeHandler.h"
#include "GMTool/NSGMToolManager.h"
#include "CNLGlobalPipe.h"
#include "Network/LocalAgent/NSGateServerLocalAgentConnection.h"

extern void InitializeHttp(uint16_t port); // placed on the Initialize/InitializeHttp.cpp

extern void AppendWindowConsoleLog(); // placed on the InitializeLog.cpp
int setXorKey = InitEncKeyTable(g_XOREncryptKey);


static bool InitializeGlobalPipeToLocalAgent()
{
	//로컬 에이전트 전역 객체를 초기화 합니다.
	//객체 할당 실패시에만 false를 리턴.
	//연결 실패시 이후 루프에서 재연결이 시도됨.

	CNLAsioSocket::IOOption optionRemoteAgent;
	optionRemoteAgent.RecvBufferSize = 65536;
	optionRemoteAgent.SendBufferSize = 131072;

	if (CNLGlobalPipe<NSGateServerLocalAgentConnection>::Initialize(
		"127.0.0.1", /*루프백만 넣으세요.*/
		4304, /* 포트 4304 */
		262144, optionRemoteAgent, &NSGateServerLocalAgentConnection::SendMessageToLocalAgent) == false)
	{
		LOGE << "Failed to start CNLGlobalPipe<NSGameServerLocalAgentConnection> " << CNLGetLastError();
		return false;
	}

	LOGI << "Connecting to local agent 127.0.0.1::4304...";
	if (CNLGlobalPipe<NSGateServerLocalAgentConnection>::ConnectAndWait() == false)
	{
		LOGI << "Failed to connect to local agent 127.0.0.1::4304 " << CNLGetLastError();
	}
	else
	{
		LOGI << "Successfully connected to local agent 127.0.0.1::4304";
	}

	return true;
}
void SendBootUpInfoToLocalAgent()
{
	const ServerConfig& config = NSConfigManager::GetConfigs();
	const Service* service = Service::GetInstance();

	AGENT_MESSAGE_STREAM << std::format("GateServer. SID: {0}. init Start", service->GetSid());
	AGENT_MESSAGE_STREAM << std::format("Server Environment: {} Ver: {}", service->GetEnv(), service->GetVersion());
	AGENT_MESSAGE_STREAM << std::format("Serivce Id {0}", service->GetServiceId());
	AGENT_MESSAGE_STREAM << std::format("Serivce Name {0}", service->GetPublicName());
	AGENT_MESSAGE_STREAM << std::format("Service Port {0}:{1}", service->GetPublicDomain(), service->GetPublicPort());
	AGENT_MESSAGE_STREAM << std::format("Protocol Checksum: Protocol(0x{:x}) Struct(0x{:x})", ProjectVersion::GetProtocolSchemeChecksum(), ProjectVersion::GetProtocolStructChecksum());
	AGENT_MESSAGE_STREAM << std::format("Consul: {}:{}", service->GetContext().Consul.Host, service->GetContext().Consul.Port);
	AGENT_MESSAGE_STREAM << std::format("JsonPath: {}", System::GetInstance()->GetTableDirPath());
	AGENT_MESSAGE_STREAM << std::format("Reset TimeZone Offset: {:+d}", config.Common.Application.TimeZoneOffset);
	AGENT_MESSAGE_STREAM << std::format("Serivce Id {0}", Service::GetInstance()->GetServiceId());
	AGENT_MESSAGE_STREAM << std::format("GamePort: {0}", config.GateServer.WorldPort);
	AGENT_MESSAGE_STREAM << std::format("ClientPort: {0}", config.GateServer.ClientPort);
	AGENT_MESSAGE_STREAM << std::format("APIPort: {0}", config.GateServer.APIPort);

}




static bool LoadDataBase()
{
	const SystemContext& context = System::GetInstance()->GetContext();
	const CommonConfig::DataBaseProp& gameDB = NSConfigManager::GetConfigs().Common.GameDB;
	const CommonConfig::DataBaseProp& commonDB = NSConfigManager::GetConfigs().Common.CommonDB;
	//DataBaseInit
	LOGI << "DatabaseManager Init Start";

	if (!NSDataBaseManager::GetInstance()->AddConnectionInfo(EDataBase::Game, System::GetInstance()->GetDBProvider(),
		gameDB.Host.c_str(), gameDB.Port, gameDB.DBName.c_str(), gameDB.ID.c_str(), gameDB.Password.c_str(), context.Database.WorkThreadCount))
	{
		LOGE << std::format("msg=Database connection failed, Host={}, Port={}, DBName={}", gameDB.Host, gameDB.Port, gameDB.DBName);
		return false;
	}

	//GMToolDataBaseInit
	if (!NSDataBaseManager::GetInstance()->AddConnectionInfo(EDataBase::Common, System::GetInstance()->GetDBProvider(),
		commonDB.Host.c_str(), commonDB.Port, commonDB.DBName.c_str(), commonDB.ID.c_str(), commonDB.Password.c_str(), commonDB.PoolCount))
	{
		LOGE << std::format("msg=Database connection failed, Host={}, Port={}, DBName={}", commonDB.Host, commonDB.Port, commonDB.DBName);
		return false;
	}

	NSDataBaseManager::GetInstance()->SetAfterExecuteQuery([](const std::shared_ptr<NSQueryData>& queryData)
		{
			NSAdoCommand* command = queryData->GetAdoCommand();
			if (command != nullptr)
			{
				const std::chrono::high_resolution_clock::duration& elapsed = queryData->GetElapsedTime();
				if (elapsed > std::chrono::milliseconds(NSDataBaseManager::HEAVY_QUERY_THRESHOLD_MS))
				{
					LOGW << std::format("Heavy query detected. Command: {}, Execution time: {} ms", command->GetCommandName(), std::chrono::duration_cast<std::chrono::milliseconds>(elapsed).count());
					NSLogManager::GetInstance().Log(LOG_ID_KG_DB_QUERY_EXECUTION, NSLogLevel::Warn
						, std::format("msg=Heavy query detected. Command: {}, Execution time: {} ms", command->GetCommandName(), std::chrono::duration_cast<std::chrono::milliseconds>(elapsed).count()));
				}

				NSLogManager::GetInstance().Log(LOG_ID_DB_QUERY_PERFORMANCE,
					NSLogLevel::Info, std::format("{} {}", command->GetCommandName(), elapsed.count()));

				std::string queryStr;
				NSLogManager::GetInstance().Log(LOG_ID_DB_QUERY_EXECUTION, NSLogLevel::Info, command->GetQueryString(queryStr));
			}
		});

	//DataBase 처리 시작
	NSDataBaseManager::GetInstance()->Start(context.Database.WorkThreadCount);
	NSGMToolManager::GetInstance()->LoadWorldNoticeInfo();
	NSGMToolManager::GetInstance()->LoadMaintenanceInfo();

	LOGI << std::format("msg=Database connection successful, Host={}, Port={}, DBName={}", gameDB.Host, gameDB.Port, gameDB.DBName);
	return true;
}

static bool DBTimeSync()
{
	NSDataSerializer dataSerializer;
	dataSerializer.Create<NSQrGetDBTime>(NSQrGetDBTime::GetQuery(System::GetInstance()->GetDBProvider()));
	std::shared_ptr<NSQueryData> queryData = NSDataBaseManager::GetInstance()->StartQuery<NSQrGetDBTime>(dataSerializer).Get();
	if (!queryData->IsValid())
		return false;

	NSAdoRecordset* recordSet = queryData->GetAdoRecordSet();
	if (recordSet == nullptr)
		return false;

	int64_t unixTime{ 0 };
	int64_t timeZoneOffset{ 0 };
	recordSet->GetItem("UnixTime", unixTime);
	recordSet->GetItem("Offset", timeZoneOffset);

	LOGI << std::format("System UnixTime: {}", NSTickManager::GetInstance()->GetUnixTime().count());
	LOGI << std::format("DB UnixTime: {}", unixTime);
	LOGI << std::format("DB TimeZone: GMT{:+d}", timeZoneOffset / 60);
	NSTickManager::GetInstance()->SetTimeSinceEpoch(std::chrono::milliseconds(unixTime));
	NSTickManager::GetInstance()->GetLocalTime().SetTimeZoneOffset(std::chrono::minutes(timeZoneOffset));
	NSGMToolManager::GetInstance()->SetDBTimeOffset(timeZoneOffset / 60);
	return true;
}

static bool LoadGameDataTable()
{
	// 불러오기 구성
	NSDataLoader dataLoader{ System::GetInstance()->GetTableDirPath() };

	// 게임 데이터 불러오기
	std::vector<std::function<bool()>> initTasks{
		[&]() { return NSBannedWordTemplate::GetInstance()->LoadTemplate(dataLoader); },
		[&]() { return NSCharacterTemplate::GetInstance()->LoadTemplate(dataLoader); },
		[&]() { return NSGameConfigTemplate::GetInstance()->LoadTemplate(dataLoader); },
		[&]() { return NSWorldTemplate::GetInstance()->LoadTemplate(dataLoader); },
		[&]() { return NSTextTemplate::GetInstance()->LoadTemplate(dataLoader); },
		[&]() { return NSCostumeTemplate::GetInstance()->LoadTemplate(dataLoader); }
	};

	[[maybe_unused]] std::atomic<bool> anyError = false;
	auto initGameData = [&](const auto& task)
		{
			if (!task())
			{
				anyError.store(true);
			}
		};

	NpLib_TableEnumUtil::LoadEnumData();
	std::for_each(std::execution::par, initTasks.begin(), initTasks.end(), initGameData);

	NSCharacterTemplate::GetInstance()->PostLoadTemplate();

	return true;
}

int g_InitServer()
{
	// 타이머 해상도를 최소로 설정... 그래야 Sleep 계열에서 1ms 가 15ms 이상으로 처리되는 엄청난 현상을 막을 수 있다
	TIMECAPS ts;
	timeGetDevCaps(&ts, sizeof(ts));
	timeBeginPeriod(ts.wPeriodMin);

	//asio 객체 초기화.
	if (CNLAsioCore::GetInstance()->CoreStartUp() == false)
		return -1;

	//게임서버 전용 대역
	int resThreadCnt = 4;
	if (CNLAsioCore::GetInstance()->AllocateIOService(resThreadCnt) == false)
		return -1;

	resThreadCnt = 2;
	if (CNLAsioCore::GetInstance()->AllocateIOService(resThreadCnt) == false)
		return -1;

	if (InitializeGlobalPipeToLocalAgent() == false)
	{
		LOGE << "Failed to initialize global pipe to local agent";
		return -1;
	}

	AGENT_PEER->ChangeStatus("BOOT_UP_PROGRESS");
	SendBootUpInfoToLocalAgent();

	//Com 라이브러리 초기화
	HRESULT ret = CoInitialize(NULL);
	if (ret != S_OK)
	{
		LOGE << std::format("CoInitialize Error : {}", ret);
		AGENT_MESSAGE_STREAM << std::format("CoInitialize Error : {}", ret);
		return -1;
	}

	NSRandom::GetInstance()->STLRandom();
	NSTickManager::GetInstance()->Start();

	const ServerConfig& config = NSConfigManager::GetConfigs();
	const Service* service = Service::GetInstance();

	const std::optional<int32_t> instanceId = service->GetInstanceId();
	if (!instanceId)
	{
		return -1;
	}

	// Dump Agent 실행
	if (config.Common.CrashReport.EnableDumpAgent)
	{
		std::string moduleFileName;
		NSUtil::GetCurrentModuleFileName(moduleFileName);

		std::vector<std::string> additionalFiles;
#ifdef _DEBUG
		// Debug빌드면 exe와 pdb도 수집하도록 한다.
		std::string pdbFileName;
		size_t dotPos = moduleFileName.find_last_of('.');
		if (dotPos == std::string::npos)
			pdbFileName = moduleFileName + "." + "pdb";
		else
			pdbFileName = moduleFileName.substr(0, dotPos + 1) + "pdb";

		additionalFiles = {
			moduleFileName,
			pdbFileName
		};
		LOGI << std::format("Dump Agent will collect additional files: {}, {}", moduleFileName, pdbFileName);
#endif
		std::vector<std::string> filesForSentry = {
			"bootstrap_gameserver.txt",
			"bootstrap_gateserver.txt",
			"bootstrap_cummunityserver.txt"
		};
		std::string metaData = NSDumpAgentUtil::BuildMetaData(
			service->GetVersion(),
			service->GetEnv(),
			moduleFileName,
			service->GetSid(),
			service->GetPublicName(),
			NSUtil::GetCurrentPID(),
			System::GetInstance()->GetLogDirPath(),
			additionalFiles,
			filesForSentry
		);

		std::string moduleFilePath;
		NSUtil::GetCurrentModuleFilePath(moduleFilePath, true);

		NSDumpAgentUtil::StartDumpAgent(
			moduleFilePath,
			config.Common.CrashReport.DumpAgentExePath,
			metaData);
	}

	// 윈도우 콘솔 로그 추가
	AppendWindowConsoleLog();

	CNL_STOP_WATCH_STREAM_SCOPE("Boot Up http listener", AGENT_MESSAGE_STREAM)
	{
		// HttpListener 초기화
		InitializeHttp(config.GateServer.APIPort);
	}
	CNL_STOP_WATCH_STREAM_SCOPE("Boot Up http listener", AGENT_MESSAGE_STREAM)
	{
		//GuidManager 초기화
		NSGuidManager::GetInstance()->Initialize(*instanceId);
	}

	LOGI << std::format("GateServer. SID: {0}. init Start", service->GetSid());
	LOGI << std::format("Server Environment: {} Ver: {}", service->GetEnv(), service->GetVersion());
	LOGI << std::format("Serivce Id {0}", service->GetServiceId());
	LOGI << std::format("Serivce Name {0}", service->GetPublicName());
	LOGI << std::format("Service Port {0}:{1}", service->GetPublicDomain(), service->GetPublicPort());
	LOGI << std::format("Protocol Checksum: Protocol(0x{:x}) Struct(0x{:x})",
		ProjectVersion::GetProtocolSchemeChecksum(), ProjectVersion::GetProtocolStructChecksum());
	LOGI << std::format("Consul: {}:{}", service->GetContext().Consul.Host, service->GetContext().Consul.Port);
	LOGI << std::format("JsonPath: {}", System::GetInstance()->GetTableDirPath());


	CNL_STOP_WATCH_STREAM_SCOPE("Boot Up Redis Connection Pool", AGENT_MESSAGE_STREAM)
	{
		//레디스 커넥션풀 생성

		const CommonConfig::RedisProp& redis = NSConfigManager::GetConfigs().Common.Redis;
		if (!NSRedisUtil::RedisClient::GetInstance().Initialize(
			redis.Host,
			redis.Port,
			redis.Password,
			redis.PoolSize,
			redis.DBIndex,
			redis.ClusterEnable,
			redis.TLSEnable,
			redis.TLSPort))
		{
			LOGE << std::format("Initialize Redis Fail");
			AGENT_MESSAGE_STREAM << std::format("Initialize Redis Fail");
			return -1;
		}

	}


	if (config.Common.AntiCheat.EnableAntiCheat)
	{
		CNL_STOP_WATCH_STREAM_SCOPE("Boot Up XignCode", AGENT_MESSAGE_STREAM)
		{
			NSXignCodeHandler::GetInstance()->InitializeXignCode();
		}
	}

	CNL_STOP_WATCH_STREAM_SCOPE("Load Database", AGENT_MESSAGE_STREAM)
	{
		//데이터베이스 로드
		if (!LoadDataBase())
		{
			LOGE << std::format("LoadDataBase Fail");
			AGENT_MESSAGE_STREAM << std::format("LoadDataBase Fail");
			return -1;
		}
	}

	if (config.Common.Application.DBTimeSync)
	{
		DBTimeSync();
	}


	LOGI << std::format("Reset TimeZone Offset: {:+d}", config.Common.Application.TimeZoneOffset);
	NSTickManager::GetInstance()->GetLocalTime().SetTimeZoneOffset(std::chrono::hours(config.Common.Application.TimeZoneOffset));

	CNL_STOP_WATCH_STREAM_SCOPE("Load Game Data Table", AGENT_MESSAGE_STREAM)
	{
		//게임데이터 로드
		if (!LoadGameDataTable())
		{
			LOGE << std::format("LoadGameDataTable Fail");
			AGENT_MESSAGE_STREAM << std::format("LoadGameDataTable Fail");
			return -1;
		}
	}

	LOGI << "Server init Start";
	LOGI << std::format("Serivce Id {0}", Service::GetInstance()->GetServiceId());
	LOGI << std::format("GamePort: {0}", config.GateServer.WorldPort);
	LOGI << std::format("ClientPort: {0}", config.GateServer.ClientPort);
	LOGI << std::format("APIPort: {0}", config.GateServer.APIPort);


	//패킷풀
	NSPacketPool::GetInstance()->Init(10, 10, 10);

	//초기화 대상 NXNET->CNL
	//RECV / SEND 용량 제한 설정 기능 추가 되었습니다.

	//각 클라이언트 피어의 허용 송수신 용량은
	//Recv 65536 로 설정됩니다.
	//Send 524288 로 설정됩니다.

	//원격이 게임서버인 경우
	//각 게임서버 피어의 허용 송수신 용량은
	//Recv 134217728 로 설정됩니다.
	//Send 134217728 로 설정됩니다.

	CNLAsioSocket::IOOption optionRemoteClient;
	optionRemoteClient.RecvBufferSize = 65536;
	optionRemoteClient.SendBufferSize = 262144;
	optionRemoteClient.SendBufferScalableN = 4;
	optionRemoteClient.UseXOREncryption = true;
	optionRemoteClient.RecordPacketGenTime = false;

	AGENT_MESSAGE_STREAM << std::format("Client Recv Buffer Size : {0} bytes ", optionRemoteClient.RecvBufferSize);
	AGENT_MESSAGE_STREAM << std::format("Client Send Buffer Default Size : {0} bytes Scalable For {1}X", optionRemoteClient.SendBufferSize, optionRemoteClient.SendBufferScalableN);

	CNLAsioSocket::IOOption optionRemoteWorldServer;
	optionRemoteWorldServer.RecvBufferSize = 134217728;
	optionRemoteWorldServer.SendBufferSize = 134217728;
	optionRemoteWorldServer.RecordPacketGenTime = false;

	AGENT_MESSAGE_STREAM << std::format("WorldServer Recv Buffer Size : {0} bytes ", optionRemoteWorldServer.RecvBufferSize);
	AGENT_MESSAGE_STREAM << std::format("WorldServer Send Buffer Default Size : {0} bytes Scalable For {1}X", optionRemoteWorldServer.SendBufferSize, optionRemoteWorldServer.SendBufferScalableN);


	//네트워크 이벤트 버퍼로 캐시버퍼를 사용하기 때문에,
	//이것을 위한 버퍼 설정이 필요합니다.

	//우선 봇 테스트 1만에서의 숫자인 2억 바이트로 설정했습니다.
	//나머지 구현은 동일합니다.

	if (NSWorldServerHandler::GetInstance()->Initialize_Service_Logic_Adpater(200'000'000) == false)
	{
		LOGE << "Failed to allocate net buffer [NSWorldServerHandler]";
		AGENT_MESSAGE_STREAM << "Failed to allocate net buffer [NSWorldServerHandler]";
		return -1;
	}

	if (NSClientHandler::GetInstance()->Initialize_Service_Logic_Adpater(200'000'000) == false)
	{
		LOGE << "Failed to allocate net buffer [NSClientHandler]";
		AGENT_MESSAGE_STREAM << "Failed to allocate net buffer [NSClientHandler]";
		return -1;
	}

	// WorldServerSession
	{
		const uint16_t port = config.GateServer.WorldPort;
		if (CNLAsioCore::GetInstance()->Listen<CNLIOInterfaceAdapter>

			(1, "0.0.0.0", port, NSWorldServerHandler::GetInstance()->Get_Service_Logic_Adapter(), optionRemoteWorldServer) == false)
		{
			LOGE << "Failed to open port " << port << "for remote client connection err : " << CNLGetLastError();
			AGENT_MESSAGE_STREAM << "Failed to open port " << port << "for remote client connection err : " << CNLGetLastError();
			return -1;
		}

		NSGateLogicThread::GetInstance()->AddHandler(NSWorldServerHandler::GetInstance());

		LOGI << "WorldServerSession init Complete";
		LOGI << "WorldServerSession Listen [PORT:" << port << "]";

		AGENT_MESSAGE_STREAM << "WorldServerSession init Complete";
		AGENT_MESSAGE_STREAM << "WorldServerSession Listen [PORT:" << port << "]";
	}

	// ClientSession
	{
		const uint16_t port = config.GateServer.ClientPort;
		if (CNLAsioCore::GetInstance()->Listen<CNLIOInterfaceAdapter>
			(0, "0.0.0.0", port, NSClientHandler::GetInstance()->Get_Service_Logic_Adapter(), optionRemoteClient) == false)
		{
			LOGE << "Failed to open port " << port << "for remote client connection err : " << CNLGetLastError();
			AGENT_MESSAGE_STREAM << "Failed to open port " << port << "for remote client connection err : " << CNLGetLastError();
			return -1;
		}

		NSGateLogicThread::GetInstance()->AddHandler(NSClientHandler::GetInstance());

		LOGI << "ClientSession init Complete";
		LOGI << "ClientSession Listen [PORT:" << port << "]";

		AGENT_MESSAGE_STREAM << "ClientSession init Complete";
		AGENT_MESSAGE_STREAM << "ClientSession Listen [PORT:" << port << "]";

	}

	{
		NSGateLogicThread::GetInstance()->AddHandler(NSCommunityServerHandler::GetInstance());
	}

	CNLGlobalPipe<NSGateServerLocalAgentConnection>::GetPeer()->ChangeStatus("ON_SERVICE");
	NSGateLogicThread::GetInstance()->Start();

	return ERROR_SUCCESS;
}

int g_ProcessServer(wchar_t* pstrCommand)
{
	if (wcslen(pstrCommand) <= 0)
		return ERROR_SUCCESS;

	NSWorldServerHandler::GetInstance()->AddConsoleCommand(pstrCommand);

	return ERROR_SUCCESS;
}

int g_ReleaseServer()
{
	auto agentPeer = CNLGlobalPipe<NSGateServerLocalAgentConnection>::GetPeer();
	agentPeer->ChangeStatus("SHUT_DOWN_PROGRESS");


	CNL_STOP_WATCH_STREAM_SCOPE("End Up Server", AGENT_MESSAGE_STREAM)
	{
		CNL_STOP_WATCH_STREAM_SCOPE("Clean Up NSGateLogicThread", AGENT_MESSAGE_STREAM)
		{
			NSGateLogicThread::GetInstance()->Stop();
			NSGateLogicThread::DestroyInstance();
		}

		CNL_STOP_WATCH_STREAM_SCOPE("Clean Up NSWorldServerHandler", AGENT_MESSAGE_STREAM)
		{
			NSWorldServerHandler::DestroyInstance();
		}

		CNL_STOP_WATCH_STREAM_SCOPE("Clean Up NSClientHandler", AGENT_MESSAGE_STREAM)
		{
			NSClientHandler::DestroyInstance();
		}

		CNL_STOP_WATCH_STREAM_SCOPE("Clean Up NSTickManager", AGENT_MESSAGE_STREAM)
		{
			//틱메니져 제거
			NSTickManager::GetInstance()->Stop();
			NSTickManager::DestroyInstance();
		}

		CNL_STOP_WATCH_STREAM_SCOPE("Clean Up NSWorldServerManager", AGENT_MESSAGE_STREAM)
		{
			NSWorldServerManager::DestroyInstance();
		}

		CNL_STOP_WATCH_STREAM_SCOPE("Clean Up NSWorldServerManager", AGENT_MESSAGE_STREAM)
		{
			NSPacketPool::DestroyInstance();
		}

		CNL_STOP_WATCH_STREAM_SCOPE("Clean Up NSRandom", AGENT_MESSAGE_STREAM)
		{
			NSRandom::DestroyInstance();
		}

		CNL_STOP_WATCH_STREAM_SCOPE("Clean Up NSHttpListener", AGENT_MESSAGE_STREAM)
		{
			NSHttpListener::GetInstance()->Stop();
		}

		if (NSConfigManager::GetConfigs().Common.AntiCheat.EnableAntiCheat)
		{
			CNL_STOP_WATCH_STREAM_SCOPE("Clean Up NSXignCodeHandler", AGENT_MESSAGE_STREAM)
			{
				NSXignCodeHandler::GetInstance()->ReleaseXignCode();
			}
		}

		CNL_STOP_WATCH_STREAM_SCOPE("Clean Up NSXignCodeHandler", AGENT_MESSAGE_STREAM)
		{
			::CoUninitialize();

			TIMECAPS ts;
			timeGetDevCaps(&ts, sizeof(ts));
			timeEndPeriod(ts.wPeriodMin);
		}
	}

	agentPeer->ChangeStatus("SHUT_DOWN_COMPLETE");

	//wait for 2 sec to finish sending all message to local agent
	Sleep(2000);

	//네트웍정지
	CNLAsioCore::GetInstance()->CoreCleanUp();
	g_pcServerMain->SetServerState(ESERVER_STATE::END);

	return ERROR_SUCCESS;
}
