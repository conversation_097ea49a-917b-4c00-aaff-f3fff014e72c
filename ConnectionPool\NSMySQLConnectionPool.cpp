#include "../stdafx.h"
#include "NSMySQLConnectionPool.h"
#include "../Connection/NSMySQLConnection.h"
#include "../Diagnostics/NSLogger.h"
#include <algorithm>

NSMySQLConnectionPool::NSMySQLConnectionPool()
{
}

NSMySQLConnectionPool::NSMySQLConnectionPool(const std::string& host, int port, const std::string& dbName,
                                             const std::string& user, const std::string& password)
    : m_host(host)
    , m_port(port)
    , m_database(dbName)
    , m_user(user)
    , m_password(password)
{
}

NSMySQLConnectionPool::~NSMySQLConnectionPool()
{
    Finalize();
}

bool NSMySQLConnectionPool::Initialize(int databaseType, int shardId)
{
    bool expected = false;
    if (!m_initialized.compare_exchange_strong(expected, true))
        return true; // Already initialized

    m_databaseType = databaseType;
    m_shardId = shardId;

    // Load connection info if not set
    if (m_host.empty())
    {
        if (!LoadConnectionInfo())
        {
            m_initialized = false;
            return false;
        }
    }

    // Create all connections upfront
    std::lock_guard<std::mutex> lock(m_connectionsMutex);
    m_connections.reserve(m_maxConnections);
    
    for (int i = 0; i < m_maxConnections; ++i)
    {
        auto conn = CreateConnection();
        if (conn)
        {
            m_connections.push_back(conn);
        }
        else
        {
            LOGE << "Failed to create connection " << i << " for pool " 
                 << databaseType << ":" << shardId;
            
            // Clean up already created connections
            for (auto& c : m_connections)
            {
                if (c) c->Disconnect();
            }
            m_connections.clear();
            m_initialized = false;
            return false;
        }
    }

    LOGI << "ConnectionPool initialized: type=" << databaseType 
         << " shard=" << shardId 
         << " connections=" << m_connections.size();
         
    return true;
}

void NSMySQLConnectionPool::Finalize()
{
    bool expected = true;
    if (!m_initialized.compare_exchange_strong(expected, false))
        return; // Already finalized

    m_shutting_down = true;

    // Close all connections
    std::lock_guard<std::mutex> lock(m_connectionsMutex);
    for (auto& conn : m_connections)
    {
        if (conn)
        {
            conn->Disconnect();
        }
    }
    m_connections.clear();

    LOGI << "ConnectionPool finalized";
}

std::shared_ptr<NSMySQLConnection> NSMySQLConnectionPool::GetConnection()
{
    if (!m_initialized.load() || m_shutting_down.load())
        return nullptr;

    // Note: In the new architecture, ConnectionManager handles the actual connection selection
    // This method is kept for compatibility but should not be used directly
    LOGW << "GetConnection() called on pool - should use ConnectionManager instead";
    return nullptr;
}

void NSMySQLConnectionPool::ReturnConnection(std::shared_ptr<NSMySQLConnection> conn)
{
    // No-op in simplified version
    // ConnectionManager handles connection lifecycle
}

Database::ConnectionInfo NSMySQLConnectionPool::GetConnectionInfo() const
{
    return Database::ConnectionInfo(m_host, m_port, m_user, m_password, m_database);
}

bool NSMySQLConnectionPool::AddConnectionInfo(const std::string& host, int port, 
                                             const std::string& dbName, 
                                             const std::string& user, 
                                             const std::string& password)
{
    if (m_initialized.load())
    {
        LOGW << "Cannot change connection info after initialization";
        return false;
    }

    m_host = host;
    m_port = port;
    m_database = dbName;
    m_user = user;
    m_password = password;
    
    return true;
}

void NSMySQLConnectionPool::Reconnect()
{
    if (!m_initialized.load())
        return;

    std::lock_guard<std::mutex> lock(m_connectionsMutex);
    for (auto& conn : m_connections)
    {
        if (conn && !conn->IsConnected())
        {
            conn->Connect(m_host.c_str(), m_port, m_user.c_str(), 
                         m_password.c_str(), m_database.c_str());
        }
    }
}

bool NSMySQLConnectionPool::IsHealthy() const
{
    if (!m_initialized.load() || m_shutting_down.load())
        return false;

    // Simple health check - at least one connection is alive
    std::lock_guard<std::mutex> lock(m_connectionsMutex);
    for (const auto& conn : m_connections)
    {
        if (conn && conn->IsConnected())
            return true;
    }
    
    return false;
}

std::shared_ptr<NSMySQLConnection> NSMySQLConnectionPool::CreateConnection()
{
    auto conn = std::make_shared<NSMySQLConnection>();
    
    if (!conn->Connect(m_host.c_str(), m_port, m_user.c_str(), 
                      m_password.c_str(), m_database.c_str()))
    {
        LOGE << "Failed to connect to " << m_host << ":" << m_port << "/" << m_database;
        m_failedConnections++;
        return nullptr;
    }

    // Set connection options
    conn->SetAutoCommit(true);
    conn->SetCharacterSet("utf8mb4");
    
    m_totalQueries++;
    
    return conn;
}

bool NSMySQLConnectionPool::LoadConnectionInfo()
{
    // This would load from config file or environment
    // For now, return false to indicate it needs to be set manually
    return false;
}

bool NSMySQLConnectionPool::ValidateConnection(const std::shared_ptr<NSMySQLConnection>& conn)
{
    if (!conn)
        return false;

    // Simple ping to check if connection is alive
    return conn->IsConnected() && conn->Ping();
}