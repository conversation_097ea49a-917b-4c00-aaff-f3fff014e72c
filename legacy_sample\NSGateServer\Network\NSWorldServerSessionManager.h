#pragma once

#include "NSSingleton.h"
#include "NSWorldServerSession.h"

class NSWorldServerSessionManager : public TemplateSingleton<NSWorldServerSessionManager>
{
public:
	bool AddSession(std::shared_ptr<NSWorldServerSession>& session);
	auto GetSessionSize() const noexcept->size_t;
	void CloseAllSession();

	auto RemoveSession(int64_t socketChannelId)->std::shared_ptr<NSWorldServerSession>;
	auto GetSession(int64_t socketChannelId)->std::shared_ptr<NSWorldServerSession>;

private:
	std::unordered_map<int64_t, std::shared_ptr<NSWorldServerSession>> m_Session;
};

