#pragma once

#include "System/SystemBase/NSGateSystemBase.h"
#include "Network/NSClientSession.h"

class NSGateSystemBase;
class NSClientSession;
class NSPacketData;
class NSScheduledTaskManager;
class NSGateModule
{
public:
	NSGateModule();
	virtual ~NSGateModule();

	virtual void Init();
	virtual void Reset();
	virtual void Process(uint64_t nowTick, uint64_t elapsedTickCount); //ms

	virtual bool OnAddSession(std::shared_ptr<NSClientSession> session) = 0;
	virtual bool OnMoveSession(std::shared_ptr<NSClientSession> session) = 0;
	virtual bool OnRemoveSession(std::shared_ptr<NSClientSession> session) = 0;
	virtual bool OnDisconnect(std::shared_ptr<NSClientSession> session);

	auto GetModuleID() const -> int32_t;
	void SetModuleID(int32_t moduleId);

	void InsertScheduledTask(uint64_t delay, const std::function<void()> & function) const;
	void InsertScheduledTaskWithoutInstantRun(uint64_t delay, const std::function<void()> & function) const;
	void ClearScheduledTask() const;

	bool AddSession(std::shared_ptr<NSClientSession> session);
	bool MoveSession(std::shared_ptr<NSClientSession> session);
	bool RemoveSession(std::shared_ptr<NSClientSession> session);
	bool Disconnect(int64_t channelId);

	template <typename PacketType, typename Class, class... Args>
	void RegisterProcessor(Class* ptr, void(Class::* method)(Args ...), int intervalMS = 0)
	{
		RegisterProcessor<PacketType>([=](Args... args) { (ptr->*method)(std::forward<Args>(args)...); }, intervalMS);
	}

	template <typename _PacketType>
	void RegisterProcessor(std::function<void(std::shared_ptr<NSClientSession>&, _PacketType*&)> processor, int intervalMS = 0)
	{
		auto it = m_PacketProcessor.find(_PacketType::GetPacketType());
		if (it != m_PacketProcessor.end())
		{
			LOGW << std::format("Packet Callback func has been overwrited![PacketType:{}][PacketName:{}]", _PacketType::GetPacketType(), _PacketType::GetPacketName());
		}

		[[maybe_unused]] bool inserted = m_PacketProcessor.try_emplace(_PacketType::GetPacketType(), [=, this](std::shared_ptr<NSClientSession>& session, const char* buffer) {
			PacketProcessor<_PacketType>(processor, session, buffer, intervalMS);
			}).second;
		assert(inserted);

#ifdef  _DEBUG
		//LOGD << std::format("Register Packet Callback[PacketType:{}][PacketName:{}]", _PacketType::GetPacketType(), _PacketType::GetPacketName());
#endif
	}

	template <typename _PacketType>
	void PacketProcessor(std::function<void(std::shared_ptr<NSClientSession>&, _PacketType*&)> process, std::shared_ptr<NSClientSession>& session, const char* buffer, int intervalMS)
	{
		_PacketType packet;
		_PacketType* packetPtr;

		if (packet.NeedSerialize())
		{
			if (packet.Deserialize(buffer) == false)
			{
				LOGW << std::format("ReceivePacket Deserialize failed [Type:{}]", packet.GetType());
				return;
			}
			packetPtr = &packet;
		}
		else
		{
			packetPtr = (_PacketType*)buffer;
			if (packetPtr->GetSize() != packet.GetSize())
			{
				LOGW << std::format("ReceivePacket Size different [Type:{}] [ReceiveSize:{}] [PacketSize:{}]", packetPtr->GetType(), packetPtr->GetSize(), packet.GetSize());
				session->Disconnect();
				return;
			}
		}

		if (intervalMS > 0 && !session->IsDummyUser())
		{
			if (!session->CheckInputTime(packetPtr, intervalMS))
			{
				LOGW << std::format("ReceivePacket duplicate protocol [Type:{}]", packetPtr->GetType());
				return;
			}
		}

		process(session, packetPtr);
	}

	void PushPacket(NSPacketData* packetData);
	virtual void HandlePacket(NSPacketData* packetData);

	template<typename System>
	System* GetSystem()
	{
		static_assert(std::is_base_of<NSGateSystemBase, System>(),
			"System is not a SystemBase, cannot get System from Map"); // 추론된 시스템은 템플릿 오류로 확인

		auto itr = m_ProcessorSystems.find(NSClassTypeId::SystemTypeId<System>());
		if (m_ProcessorSystems.end() == itr)
			return nullptr;

		return static_cast<System*>(itr->second.get());
	}

protected:
	template<typename System, typename Class>
	bool AddSystem(Class* ptr)
	{
		static_assert(std::is_base_of<NSGateSystemBase, System>(),
			"System is not a SystemBase, cannot add System to Map"); // 추론된 시스템은 템플릿 오류로 확인

		auto typeId = NSClassTypeId::SystemTypeId<System>();
		auto itr = m_ProcessorSystems.find(typeId);
		if (m_ProcessorSystems.end() != itr)
		{
			LOGD << std::format("Duplicate System : {}", typeid(System).name());
			return false;
		}

		return m_ProcessorSystems.emplace(typeId, std::make_unique<System>(ptr)).second;
	}

	std::shared_ptr<NSClientSession> GetSessionByChannelId(int64_t channelId);


protected:
	using PROCESSHANDLE = std::function<void(std::shared_ptr<NSClientSession>&, const char*)>;

	int32_t m_ModuleId;
	std::unique_ptr<NSScheduledTaskManager> m_ScheduledTaskManager;

	std::unordered_map<int64_t, std::shared_ptr<NSClientSession>> m_SessionByChannelId;
	std::queue<NSPacketData*> m_QueuePacketData;
	std::unordered_map<uint16_t, PROCESSHANDLE> m_PacketProcessor;

	std::map<int, std::unique_ptr<NSGateSystemBase>> m_ProcessorSystems;
};
