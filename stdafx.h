#pragma once

// mimalloc 통합 (반드시 첫 번째로 포함)
#define USE_MIMALLOC
#include "mimalloc_integration.h"

// Windows
#define WIN32_LEAN_AND_MEAN
#define NOMINMAX
#include <windows.h>
#include <winsock2.h>

// STL
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <thread>
#include <functional>
#include <variant>
#include <exception>

// MySQL
#include <mysql.h>

// 게임서버 헤더 (레거시 ADO와 동일)
#include "NPErrorCode.h"
#include "NSDefine.h"
#include "NSLogDefine.h"

// 타입 정의는 게임서버의 NSDefine.h에서 제공