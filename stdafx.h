#pragma once

// mimalloc 통합 (반드시 첫 번째로 포함)
#define USE_MIMALLOC
#include "mimalloc_integration.h"

// Windows
#define WIN32_LEAN_AND_MEAN
#define NOMINMAX
#include <windows.h>
#include <winsock2.h>

// STL
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <thread>
#include <functional>
#include <variant>
#include <exception>

// MySQL
#include <mysql.h>

// Common types
using int32 = int32_t;
using int64 = int64_t;
using uint32 = uint32_t;
using uint64 = uint64_t;