#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpCreateCharacter : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spCreateCharacter";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);
	EErrorCode HandleReturnValue(const int32_t returnValue);

public:
	struct Input : NSInput_HasReward
	{
		int64_t Aid;
		int64_t Cid;
		int32_t Wid;
		char Name[g_uMaxCharacterNameUTF8Length];
		ENpLib_CharacterClassType ClassType;
		uint8_t Gender;
		int32_t Height;

		int32_t DefaultChronotectorNodeId = 0;
		int32_t NodePoint = 0;
		char DefaultCharacterGoods[4000] = { "{}" };
		char DefaultClasses[4000] = { "{}" };
		char DefaultEquipmentJson[4000] = { "{}" };
		char DefaultMainQuestJson[4000] = { "{}" };
		char DefaultWeaponMastery[4000] = { "{}" };
		char CustomizePayload[8000] = { "{}" };
		char DefaultChronotectorActionJson[4000] = { "{}" };
	} Input;

	SpCreateCharacter() = default;
	SpCreateCharacter(const int64_t aid, const int64_t cid, const int32_t wid, const char* name, const ENpLib_CharacterClassType classType, const uint8_t gender, const int32_t height, const char* customizePayload);
};
