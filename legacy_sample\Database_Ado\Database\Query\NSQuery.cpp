#include "stdafx.h"
#include "NSQuery.h"

NSQuery::NSQuery(EDataBase dbType, std::string_view query) :
	m_DBType(dbType), m_Query(query)
{
}

auto NSQuery::QueryFunc(const std::shared_ptr<NSQueryData> queryData) -> EErrorCode
{
	NSAdoConnection* connection = NSDataBaseManager::GetInstance()->GetDBConnection(m_DBType);
	if (connection == nullptr)
		return EErrorCode::DBConnectionError;

	NSAdoRecordset* recordSet = queryData->GetAdoRecordSet();
	if (recordSet == nullptr)
		return EErrorCode::DBError;

	if (!recordSet->Open(connection, m_Query.c_str()))
		return EErrorCode::DBError;

	return EErrorCode::None;
}
