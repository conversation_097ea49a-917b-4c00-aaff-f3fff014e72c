#include "stdafx.h"
#include "NSAdoConnection.h"

NSAdoConnection::NSAdoConnection()
	: m_Connection(nullptr)
{
	m_Connection.CreateInstance(__uuidof(Connection));
	m_Connection->CursorLocation = adUseClient; //커서는 클라이언트 커서 사용
	memset(m_ConnectionInfo, 0, sizeof(m_ConnectionInfo));
}

NSAdoConnection::~NSAdoConnection()
{
	//컨맨드 객체 메모리 해제
	for (NSAdoCommand* command : m_Commands | srv::values)
	{
		if (command == nullptr)
			continue;

		delete command;
		command = nullptr;
	}

	m_Commands.clear();

	if (m_Connection != nullptr)
	{
		if (m_Connection->GetState() == adStateOpen)
			m_Connection->Close();

		m_Connection.Release();
		m_Connection = nullptr;
	}
}

void NSAdoConnection::dump_com_error(const _com_error& e)
{
	_bstr_t bstrSource(e.Source());
	_bstr_t bstrDescription(e.Description());
	LOGE << std::format("AdoConnection Error Code = {:#08x}, Code meaning = {}, Source = {}, Description = {}, connetion={}",
		e.Error(), (LPCSTR)e.ErrorMessage(), (LPCSTR)bstrSource, (LPCSTR)bstrDescription, m_ConnectionInfo);
}

bool NSAdoConnection::Connect()
{
	if (m_isConnecting)
		return false;

	try
	{
		m_isConnecting = true;
		m_Connection->CommandTimeout = 5;
		m_Connection->ConnectionTimeout = 30;
		HRESULT result = m_Connection->Open(m_ConnectionInfo, "", "", 0);
		if (result != S_OK)
		{
			LOGE << std::format("Connetion Fail - Result :{}", result);
			m_isConnecting = false;
			return false;
		}

		m_Connection->CursorLocation = adUseClient;

		//컨맨드 객체 커넥션연결
		for (NSAdoCommand* command : m_Commands | srv::values)
		{
			command->GetCommand()->ActiveConnection = m_Connection;
		}
		m_isConnecting = false;
	}
	catch (_com_error& e)
	{
		m_isConnecting = false;
		dump_com_error(e);
		return false;
	}

	return true;
}

bool NSAdoConnection::Connect(const char* connectionString)
{
	strncpy_s(m_ConnectionInfo, _countof(m_ConnectionInfo), connectionString, _TRUNCATE);
	return Connect();
}

void NSAdoConnection::BeginTrans()
{
	m_Connection->BeginTrans();
}

void NSAdoConnection::CommitTrans()
{
	m_Connection->CommitTrans();
}

void NSAdoConnection::RollbackTrans()
{
	m_Connection->RollbackTrans();
}

auto NSAdoConnection::ExecuteQuery(const std::string_view query) -> std::unique_ptr<NSAdoRecordset>
{
	std::unique_ptr<NSAdoRecordset> recordSet = std::make_unique<NSAdoRecordset>();
	if (recordSet->Open(this, query.data()))
		return recordSet;

	return nullptr;
}

auto NSAdoConnection::GetCommand(const std::string_view commandName) -> NSAdoCommand*
{
	auto itr_find = m_Commands.find(commandName.data());
	if (itr_find == m_Commands.end())
	{
		LOGE << std::format("Command is Not Found [CommandName: {}]", commandName);
		return nullptr;
	}

	if (m_Connection->GetState() != adStateOpen)
	{
		//연결 해지 상태라면 연결해주자
		Connect();
	}

	//파라미터 리셋
	itr_find->second->Reset();

	itr_find->second->SetConnection(m_Connection);

	return itr_find->second;
}

bool NSAdoConnection::AddCommand(const std::string_view commandName, NSAdoCommand* commandObj)
{
	//커맨드 추가
	auto itr_find = m_Commands.find(commandName.data());
	if (itr_find != m_Commands.end())
		return false;

	m_Commands.insert(std::pair<std::string, NSAdoCommand*>(commandName, commandObj));
	commandObj->SetCommandName(commandName.data());
	return false;
}
