#include "stdafx.h"
#include "SpGetCharacterCustomizeListForLobby.h"

#include "ADO/NSAdoCommand.h"

SpGetCharacterCustomizeListForLobby::SpGetCharacterCustomizeListForLobby(const int64_t aid, const int64_t NowTick) :
	Input{ aid, NowTick }
{
}

EErrorCode SpGetCharacterCustomizeListForLobby::MakeQ<PERSON>y(NSAdoCommand* command)
{
	command->SetItem("AID", Input.Aid);
	//command->SetItem("@NowTick", Input.NowTick);

	return EErrorCode::None;
}
