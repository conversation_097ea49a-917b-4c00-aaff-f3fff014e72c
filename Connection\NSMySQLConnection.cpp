#include "../stdafx.h"
#include "NSMySQLConnection.h"
#include "../MySQLCommand.h"
#include "../RecordSet.h"
#include "../MySQLCompatibility.h"
#include <sstream>
#include <chrono>

NSMySQLConnection::NSMySQLConnection()
    : m_mysql(mysql_init(nullptr), MysqlDeleter())
{
    if (!m_mysql) {
        throw std::runtime_error("Failed to initialize MySQL connection");
    }
}

NSMySQLConnection::~NSMySQLConnection()
{
    Disconnect();
}

bool NSMySQLConnection::Connect(const char* host, int port, const char* user,
                                const char* password, const char* database)
{
    if (!m_mysql)
        return false;

    // 연결 정보 저장
    m_host = host;
    m_port = port;
    m_user = user;
    m_password = password;
    m_database = database;

    // 연결 옵션 설정
    mysql_options(m_mysql.get(), MYSQL_OPT_CONNECT_TIMEOUT, "5");
    mysql_options(m_mysql.get(), MYSQL_OPT_READ_TIMEOUT, "30");
    mysql_options(m_mysql.get(), MYSQL_OPT_WRITE_TIMEOUT, "30");
    
    // MariaDB 전용 - 자동 재연결 비활성화

    // 연결
    if (!mysql_real_connect(m_mysql.get(), host, user, password, database, port, nullptr, 0))
    {
        m_connected = false;
        return false;
    }

    // UTF8 설정
    mysql_set_character_set(m_mysql.get(), "utf8mb4");
    
    m_connected = true;
    return true;
}

void NSMySQLConnection::Disconnect()
{
    m_connected = false;
    
    // 캐시된 statement들 정리
    ClearStatementCache();
    
    // 현재 statement 참조 제거
    m_currentStmt = nullptr;
    
    // 결과셋 자동 정리 (unique_ptr)
    m_result.reset();
    
    // 메타데이터 캐시 정리
    {
        std::lock_guard<std::mutex> lock(m_metadataMutex);
        m_metadataCache.clear();
    }
}

bool NSMySQLConnection::Reconnect()
{
    // 기존 연결 닫기
    m_connected = false;
    m_mysql.reset();
    
    // MYSQL 구조체 재초기화
    m_mysql.reset(mysql_init(nullptr));
    if (!m_mysql)
        return false;

    // 재연결
    return Connect(m_host.c_str(), m_port, m_user.c_str(), 
                   m_password.c_str(), m_database.c_str());
}

bool NSMySQLConnection::IsConnected() const
{
    // thread-safe atomic 읽기
    return m_mysql && m_connected.load();
}

bool NSMySQLConnection::CheckConnection()
{
    if (!m_mysql)
        return false;
        
    // 연결 상태가 닫혀있으면 재연결 시도
    if (!m_connected.load())
    {
        return Reconnect();
    }
    
    // 간단한 쿼리로 연결 확인 (SELECT 1)
    if (mysql_query(m_mysql.get(), "SELECT 1") != 0)
    {
        m_connected = false;
        return Reconnect();
    }
    
    // 결과 소비 (RAII로 자동 정리)
    std::unique_ptr<MYSQL_RES, MysqlResDeleter> result(
        mysql_store_result(m_mysql.get()), MysqlResDeleter());
    
    return true;
}

bool NSMySQLConnection::ExecuteProcedure(const std::string& procName, MySQLCommand* command)
{
    if (!m_mysql || !command)
        return false;
        
    // DB 호출 시점에 연결 확인 (ADO 방식)
    if (!CheckConnection())
        return false;

    // 프로시저 메타데이터 가져오기
    auto metadata = GetProcedureMetadata(procName);
    if (!metadata)
    {
        if (!LoadProcedureMetadata(procName))
            return false;
        metadata = GetProcedureMetadata(procName);
    }

    // CALL 문 생성
    std::stringstream ss;
    ss << "CALL " << procName << "(";
    
    for (size_t i = 0; i < metadata->parameters.size(); ++i)
    {
        if (i > 0) ss << ", ";
        ss << "?";
    }
    ss << ")";

    std::string query = ss.str();

    // PreparedStatement 캐시에서 가져오기
    m_currentStmt = GetCachedStatement(query);
    if (!m_currentStmt)
        return false;

    // 파라미터 바인딩
    std::vector<MYSQL_BIND> binds(metadata->parameters.size());
    command->BindToMySQL(m_currentStmt, binds);

    // 실행
    if (mysql_stmt_execute(m_currentStmt) != 0)
    {
        return false;
    }

    // OUT 파라미터 가져오기
    command->RetrieveOutputParameters(m_currentStmt);

    return true;
}

bool NSMySQLConnection::ExecuteQuery(const std::string& query)
{
    if (!m_mysql)
        return false;

    return mysql_query(m_mysql, query.c_str()) == 0;
}

std::unique_ptr<RecordSet> NSMySQLConnection::GetRecordSet()
{
    if (!m_mysql)
        return nullptr;

    if (m_result)
    {
        mysql_free_result(m_result);
        m_result = nullptr;
    }

    // Statement 결과인 경우
    if (m_currentStmt)
    {
        m_result = mysql_stmt_result_metadata(m_currentStmt);
        if (m_result)
        {
            // 결과를 클라이언트로 저장
            if (mysql_stmt_store_result(m_currentStmt) == 0)
            {
                return std::make_unique<RecordSet>(m_currentStmt, m_result);
            }
        }
    }
    // 일반 쿼리 결과인 경우
    else
    {
        m_result = mysql_store_result(m_mysql);
        if (m_result)
        {
            return std::make_unique<RecordSet>(m_mysql, m_result);
        }
    }

    return nullptr;
}

bool NSMySQLConnection::BeginTransaction()
{
    return ExecuteQuery("START TRANSACTION");
}

bool NSMySQLConnection::CommitTransaction()
{
    return ExecuteQuery("COMMIT");
}

bool NSMySQLConnection::RollbackTransaction()
{
    return ExecuteQuery("ROLLBACK");
}

bool NSMySQLConnection::ExecuteProcedureAsync(const std::string& procName, MySQLCommand* command)
{
    if (!m_mysql || !command)
        return false;

    // 프로시저 메타데이터 가져오기
    auto metadata = GetProcedureMetadata(procName);
    if (!metadata)
    {
        if (!LoadProcedureMetadata(procName))
            return false;
        metadata = GetProcedureMetadata(procName);
    }

    // CALL 문 생성
    std::stringstream ss;
    ss << "CALL " << procName << "(";
    
    for (size_t i = 0; i < metadata->parameters.size(); ++i)
    {
        if (i > 0) ss << ", ";
        ss << "?";
    }
    ss << ")";

    std::string query = ss.str();

    // PreparedStatement 캐시에서 가져오기
    m_currentStmt = GetCachedStatement(query);
    if (!m_currentStmt)
        return false;
    
    // 논블로킹 속성 설정
    mysql_stmt_attr_set(m_currentStmt, STMT_ATTR_ASYNC_ENABLE, (const void*)&m_asyncExecuting);

    // 파라미터 바인딩
    std::vector<MYSQL_BIND> binds(metadata->parameters.size());
    command->BindToMySQL(m_currentStmt, binds);

    // 비동기 실행 시작
    m_asyncExecuting = true;
    int status = 0;
    
    // mysql_stmt_execute_start 사용 (논블로킹)
    status = mysql_stmt_execute_start(&m_asyncStatus, m_currentStmt);
    
    if (status == 0)
    {
        // 즉시 완료됨
        m_asyncExecuting = false;
        command->RetrieveOutputParameters(m_currentStmt);
        return true;
    }
    
    // 비동기 처리 중
    return true;
}

bool NSMySQLConnection::PollAsyncResult()
{
    if (!m_asyncExecuting)
        return true;

    int status = 0;
    
    // mysql_stmt_execute_cont 사용하여 비동기 실행 계속
    status = mysql_stmt_execute_cont(&m_asyncStatus, m_currentStmt, 0);
    
    if (status == 0)
    {
        // 실행 완료
        m_asyncExecuting = false;
        return true;
    }
    
    // 아직 처리 중
    return false;
}

const char* NSMySQLConnection::GetLastError() const
{
    if (m_currentStmt)
        return mysql_stmt_error(m_currentStmt);
    if (m_mysql)
        return mysql_error(m_mysql.get());
    return "No error";
}

int NSMySQLConnection::GetLastErrorCode() const
{
    if (m_currentStmt)
        return mysql_stmt_errno(m_currentStmt);
    if (m_mysql)
        return mysql_errno(m_mysql.get());
    return 0;
}

bool NSMySQLConnection::LoadProcedureMetadata(const std::string& procName)
{
    // information_schema에서 프로시저 파라미터 정보 조회
    std::stringstream query;
    query << "SELECT PARAMETER_NAME, DATA_TYPE, PARAMETER_MODE "
          << "FROM information_schema.PARAMETERS "
          << "WHERE SPECIFIC_SCHEMA = '" << m_database << "' "
          << "AND SPECIFIC_NAME = '" << procName << "' "
          << "ORDER BY ORDINAL_POSITION";

    if (!ExecuteQuery(query.str()))
        return false;

    std::unique_ptr<MYSQL_RES, MysqlResDeleter> result(
        mysql_store_result(m_mysql.get()), MysqlResDeleter());
    if (!result)
        return false;

    auto metadata = std::make_shared<ProcedureMetadata>();
    MYSQL_ROW row;
    
    while ((row = mysql_fetch_row(result.get())))
    {
        ProcedureMetadata::Parameter param;
        param.name = row[0] ? row[0] : "";
        
        // 데이터 타입 매핑
        std::string dataType = row[1] ? row[1] : "";
        if (dataType == "int") param.type = MYSQL_TYPE_LONG;
        else if (dataType == "bigint") param.type = MYSQL_TYPE_LONGLONG;
        else if (dataType == "varchar") param.type = MYSQL_TYPE_VAR_STRING;
        else if (dataType == "datetime") param.type = MYSQL_TYPE_DATETIME;
        else param.type = MYSQL_TYPE_STRING;
        
        // 파라미터 모드
        std::string mode = row[2] ? row[2] : "";
        param.isOutput = (mode == "OUT" || mode == "INOUT");
        
        metadata->parameters.push_back(param);
    }

    // 캐시에 저장
    std::lock_guard<std::mutex> lock(m_metadataMutex);
    m_metadataCache[procName] = metadata;
    
    return true;
}

std::shared_ptr<NSMySQLConnection::ProcedureMetadata> NSMySQLConnection::GetProcedureMetadata(const std::string& procName)
{
    std::lock_guard<std::mutex> lock(m_metadataMutex);
    auto it = m_metadataCache.find(procName);
    if (it != m_metadataCache.end())
        return it->second;
    return nullptr;
}

void NSMySQLConnection::ClearStatementCache()
{
    std::lock_guard<std::mutex> lock(m_stmtCacheMutex);
    
    // unique_ptr가 자동으로 mysql_stmt_close 호출
    m_stmtCache.clear();
    m_lruList.clear();
}

void NSMySQLConnection::EvictOldestStatement()
{
    // 호출자가 이미 m_stmtCacheMutex를 잠근 상태여야 함
    
    if (m_lruList.empty())
        return;
        
    // LRU 리스트의 마지막 항목이 가장 오래된 것
    std::string oldestQuery = m_lruList.back();
    m_lruList.pop_back();
    
    auto it = m_stmtCache.find(oldestQuery);
    if (it != m_stmtCache.end())
    {
        // unique_ptr가 자동으로 정리
        m_stmtCache.erase(it);
    }
}

MYSQL_STMT* NSMySQLConnection::GetCachedStatement(const std::string& query)
{
    std::lock_guard<std::mutex> lock(m_stmtCacheMutex);
    
    auto it = m_stmtCache.find(query);
    if (it != m_stmtCache.end())
    {
        // 캐시된 statement 사용
        it->second.lastUsed = std::chrono::steady_clock::now();
        
        // LRU 리스트에서 현재 항목을 앞으로 이동
        m_lruList.erase(it->second.lruIter);
        m_lruList.push_front(query);
        it->second.lruIter = m_lruList.begin();
        
        return it->second.stmt.get();
    }
    
    // 새 statement 준비
    auto stmt = std::unique_ptr<MYSQL_STMT, decltype(&mysql_stmt_close)>(
        mysql_stmt_init(m_mysql.get()), mysql_stmt_close);
        
    if (!stmt)
        return nullptr;
        
    if (mysql_stmt_prepare(stmt.get(), query.c_str(), query.length()) != 0)
        return nullptr;
    
    // 캐시가 가득 찬 경우 가장 오래된 항목 제거
    if (m_stmtCache.size() >= MAX_STMT_CACHE_SIZE)
    {
        EvictOldestStatement();
    }
    
    // 새 항목 추가
    m_lruList.push_front(query);
    
    StmtCacheEntry entry;
    entry.stmt = std::move(stmt);
    entry.query = query;
    entry.lastUsed = std::chrono::steady_clock::now();
    entry.lruIter = m_lruList.begin();
    
    auto* rawStmt = entry.stmt.get();
    m_stmtCache[query] = std::move(entry);
    
    return rawStmt;
}