#include "ConnectionManager.h"
#include "NSMySQLConnection.h"
#include "ConnectionInfo.h"
#include "Diagnostics/NSLogger.h"
#include <algorithm>

namespace Database
{

ConnectionManager::ConnectionManager()
{
}

ConnectionManager::~ConnectionManager()
{
    Shutdown();
}

bool ConnectionManager::Initialize(const ConnectionInfo& connInfo, int connectionCount)
{
    if (m_initialized.load())
    {
        LOGW << "ConnectionManager already initialized";
        return true;
    }

    if (connectionCount <= 0)
    {
        LOGE << "Invalid connection count: " << connectionCount;
        return false;
    }

    // Store connection info
    m_host = connInfo.host;
    m_port = connInfo.port;
    m_user = connInfo.user;
    m_password = connInfo.password;
    m_database = connInfo.database;
    m_charset = connInfo.charset;
    m_connectionFlags = connInfo.connectionFlags;

    // Create connections
    m_connections.reserve(connectionCount);
    
    for (int i = 0; i < connectionCount; ++i)
    {
        auto conn = std::make_shared<NSMySQLConnection>();
        
        if (!conn->Connect(m_host.c_str(), m_port, m_user.c_str(), 
                          m_password.c_str(), m_database.c_str(), 
                          m_charset.c_str(), m_connectionFlags))
        {
            LOGE << "Failed to create connection " << i << " to " 
                 << m_host << ":" << m_port << "/" << m_database;
            
            // Cleanup already created connections
            Shutdown();
            return false;
        }
        
        m_connections.push_back(conn);
        LOGD << "Created connection " << i << " to " << m_database;
    }

    m_initialized.store(true);
    LOGI << "ConnectionManager initialized with " << connectionCount << " connections";
    return true;
}

void ConnectionManager::Shutdown()
{
    if (!m_initialized.exchange(false))
    {
        return;
    }

    LOGD << "Shutting down ConnectionManager with " << m_connections.size() << " connections";
    
    // Close all connections
    for (auto& conn : m_connections)
    {
        if (conn)
        {
            conn->Disconnect();
        }
    }
    
    m_connections.clear();
    m_nextConnection.store(0);
    
    LOGI << "ConnectionManager shutdown complete. Total connections used: " 
         << m_totalConnectionsUsed.load();
}

std::shared_ptr<NSMySQLConnection> ConnectionManager::GetNextConnection()
{
    if (!m_initialized.load() || m_connections.empty())
    {
        LOGE << "ConnectionManager not initialized or no connections available";
        return nullptr;
    }

    // Round-robin connection selection
    size_t index = m_nextConnection.fetch_add(1) % m_connections.size();
    m_totalConnectionsUsed.fetch_add(1);
    
    auto conn = m_connections[index];
    
    // Verify connection is still valid
    if (!conn || !conn->IsConnected())
    {
        LOGW << "Connection " << index << " is not valid, attempting to reconnect";
        
        if (!conn)
        {
            conn = std::make_shared<NSMySQLConnection>();
            m_connections[index] = conn;
        }
        
        if (!conn->Connect(m_host.c_str(), m_port, m_user.c_str(), 
                          m_password.c_str(), m_database.c_str(), 
                          m_charset.c_str(), m_connectionFlags))
        {
            LOGE << "Failed to reconnect connection " << index;
            return nullptr;
        }
    }
    
    return conn;
}

void ConnectionManager::GetConnectionStats(std::vector<std::pair<size_t, uint64_t>>& stats) const
{
    stats.clear();
    stats.reserve(m_connections.size());
    
    for (size_t i = 0; i < m_connections.size(); ++i)
    {
        if (m_connections[i])
        {
            // Get usage count for each connection (would need to track this in NSMySQLConnection)
            // For now, just report connection index and whether it's connected
            stats.emplace_back(i, m_connections[i]->IsConnected() ? 1 : 0);
        }
    }
}

} // namespace Database