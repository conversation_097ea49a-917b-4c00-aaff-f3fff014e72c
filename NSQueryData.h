#pragma once
#include <memory>
#include <string>
#include <functional>
#include <unordered_map>
#include <chrono>
#include "NSDefine.h"
#include "NSDataSerializer.h"

// Forward declarations
class RecordSet;
class NSDBSession;
class NSQueryData;

// 레거시 타입 정의
using TYPE_QUERY_FUNC = std::function<EErrorCode(std::shared_ptr<NSQueryData>)>;
using TYPE_RESULT_FUNC = std::function<void(std::shared_ptr<NSQueryData>)>;

#define QUERY_FUNC(TARGET, QUERYFUNC) std::bind(QUERYFUNC, TARGET, std::placeholders::_1)
#define RESULT_FUNC(TARGET, RESULTFUNC) std::bind(RESULTFUNC, TARGET, std::placeholders::_1)

constexpr uint32_t g_uMaxQueryRecordSetCount = 64;

// 쿼리 결과 데이터
class NSQueryData : public std::enable_shared_from_this<NSQueryData>
{
public:
    using MAP_RECORDSET = std::unordered_map<std::string, RecordSet*>;
    using MAP_RETURNVALUE = std::unordered_map<std::string, int>;
    using MAP_COMMANDNAME = std::unordered_map<int, std::string>;

public:
    NSQueryData();
    NSQueryData(const char* function, uint64_t line,
        std::shared_ptr<NSDBSession> session = {});
    NSQueryData(const wchar_t* functionName, int line,
        const TYPE_QUERY_FUNC& queryFunc,
        const TYPE_RESULT_FUNC& resultFunc,
        std::shared_ptr<NSDBSession> session = {});
    virtual ~NSQueryData();
    void Reset();

    // 레거시 기능
    NSDataSerializer& GetQueryData() { return *m_pcDataSerializer; }
    RecordSet* GetRecordSet();
    RecordSet* GetRecordSet(int iIndex);
    RecordSet* GetRecordSet(const char* strCommandName);
    virtual void RunProcQuery();
    virtual void RunResultQuery();

    void SetRecordSet(std::string strCommandName, std::unique_ptr<RecordSet> pcRecordSet);
    void SetReturnValue(int iIndex, std::string strCommandName, int iReturnValue);
    const char* GetCommandName(int iIndex = 0);
    const int GetQuerySetCount() const { return m_iQuerySetCount; }
    
    int GetReturnValue();
    int GetReturnValue(int iIndex);
    int GetReturnValue(const char* strCommandName);

    void SetErrorCode(const EErrorCode eErrorCode) { m_eErrorCode = eErrorCode; }
    EErrorCode GetErrorCode() const { return m_eErrorCode; }
    bool IsValid() const { return m_eErrorCode == EErrorCode::None; }

    // 성능 측정
    void SetElapsedTime(std::chrono::high_resolution_clock::duration elapsedTime) { m_ElapsedTime = elapsedTime; }
    auto GetElapsedTime() const->std::chrono::high_resolution_clock::duration { return m_ElapsedTime; }

    // 새로운 간단 인터페이스 (하위 호환성)
    void SetRecordSet(std::unique_ptr<RecordSet> recordSet);
    void SetSuccess() { m_eErrorCode = EErrorCode::None; }
    void SetError(int errorCode, const std::string& errorMsg);
    bool IsSuccess() const { return m_eErrorCode == EErrorCode::None; }
    bool HasError() const { return m_eErrorCode != EErrorCode::None; }
    void SetRetryCount(int count) { m_retryCount = count; }
    int GetRetryCount() const { return m_retryCount; }
    
    // 세션 관리
    std::shared_ptr<NSDBSession> GetDBSession() const { return m_pSession; }
    void SetSession(std::shared_ptr<NSDBSession> session) { m_pSession = session; }
    TYPE_RESULT_FUNC GetResultFunc() const { return m_pResultFunc; }
    
    template<typename Ty>
    std::shared_ptr<Ty> GetSession() const
    {
        return std::dynamic_pointer_cast<Ty>(m_pSession);
    }

    // 디버그 정보
    void SetAllocFuncName(const wchar_t* pAllocFuncName) { m_pAllocFuncName = pAllocFuncName; }
    const wchar_t* GetAllocFuncName() { return m_pAllocFuncName; }
    void SetAllocLine(int iAllocLine) { m_iAllocLine = iAllocLine; }
    int GetAllocLine() const { return m_iAllocLine; }

protected:
    // 레거시 멤버
    NSDataSerializer* m_pcDataSerializer;
    std::unique_ptr<RecordSet> m_pcRecordset[g_uMaxQueryRecordSetCount];
    int m_iReturnValue[g_uMaxQueryRecordSetCount];
    EErrorCode m_eErrorCode;

    std::unordered_map<std::string, RecordSet*> m_mapRecordsetByName;  // raw pointer는 참조만
    MAP_RETURNVALUE m_mapReturnValueByName;
    MAP_COMMANDNAME m_mapCommandNameByIdx;

    int m_iQuerySetCount = 0;

    const wchar_t* m_pAllocFuncName = nullptr;
    int m_iAllocLine = 0;

    std::chrono::high_resolution_clock::duration m_ElapsedTime{};
    
    // NSQueryData 전용 멤버
    TYPE_QUERY_FUNC  m_pQueryFunc;
    TYPE_RESULT_FUNC m_pResultFunc;
    std::shared_ptr<NSDBSession> m_pSession;
    const char* m_Function = nullptr;
    uint64_t m_Line = 0;

    // 새 멤버 (하위 호환성)
    std::string m_errorMessage;
    int m_retryCount = 0;
};