#include "stdafx.h"
#include "NSDefineEnum.h"

#include "NSMySQLConnectionPool.h"

#include "ADO/NSAdoRecordset.h"
#include "ADO/NSAdoCommand.h"
#include "ADO/NSAdoMySQLCommand.h"

#include "Formatter/AdoEnums.h"

NSMySQLConnectionPool::NSMySQLConnectionPool(const std::string_view host, uint32_t port, const std::string_view dbName,
	const std::string_view user, const std::string_view password) :
	NSConnectionPool(host, port, dbName, user, password)
{
	m_DataTypeMap["varchar"] = DataTypeEnum::adVarChar;
	m_DataTypeMap["longtext"] = DataTypeEnum::adLongVarChar;
	m_DataTypeMap["date"] = DataTypeEnum::adDate;
	m_DataTypeMap["time"] = DataTypeEnum::adDBTime;
	m_DataTypeMap["tinyint"] = DataTypeEnum::adTinyInt;
	m_DataTypeMap["smallint"] = DataTypeEnum::adSmallInt;
	m_DataTypeMap["int"] = DataTypeEnum::adInteger;
	m_DataTypeMap["char"] = DataTypeEnum::adChar;
	m_DataTypeMap["text"] = DataTypeEnum::adLongVarChar;
	m_DataTypeMap["datetime"] = DataTypeEnum::adDBTimeStamp;
	m_DataTypeMap["float"] = DataTypeEnum::adDouble;
	m_DataTypeMap["double"] = DataTypeEnum::adDouble;
	m_DataTypeMap["decimal"] = DataTypeEnum::adNumeric;
	m_DataTypeMap["bigint"] = DataTypeEnum::adBigInt;
	m_DataTypeMap["binary"] = DataTypeEnum::adBinary;
	m_DataTypeMap["varbinary"] = DataTypeEnum::adVarBinary;
	m_DataTypeMap["bit"] = DataTypeEnum::adBoolean;
	m_DataTypeMap["nchar"] = DataTypeEnum::adChar;
	m_DataTypeMap["ntext"] = DataTypeEnum::adLongVarChar;
	m_DataTypeMap["nvarchar"] = DataTypeEnum::adVarChar;
	m_DataTypeMap["smalldatetime"] = DataTypeEnum::adDBTimeStamp;
	m_DataTypeMap["smallmoney"] = DataTypeEnum::adCurrency;
	m_DataTypeMap["sql_variant"] = DataTypeEnum::adVariant;
	m_DataTypeMap["timestamp"] = DataTypeEnum::adBinary;
	m_DataTypeMap["tinyint"] = DataTypeEnum::adTinyInt;
	m_DataTypeMap["uniqueidentifier"] = DataTypeEnum::adGUID;
	m_DataTypeMap["varbinary"] = DataTypeEnum::adVarBinary;
	m_DataTypeMap["varchar"] = DataTypeEnum::adVarChar;
	m_DataTypeMap["xml"] = DataTypeEnum::adLongVarChar;
	m_DataTypeMap["image"] = DataTypeEnum::adLongVarBinary;
	m_DataTypeMap["sql_variant"] = DataTypeEnum::adVariant;
	m_DataTypeMap["enum"] = DataTypeEnum::adVarChar;
	m_DataTypeMap["set"] = DataTypeEnum::adVarChar;
	m_DataTypeMap["json"] = DataTypeEnum::adLongVarChar;
	m_DataTypeMap["mediumtext"] = DataTypeEnum::adLongVarChar;
	m_DataTypeMap["longtext"] = DataTypeEnum::adLongVarChar;
	m_DataTypeMap["tinytext"] = DataTypeEnum::adLongVarChar;
}

NSAdoConnection* NSMySQLConnectionPool::Alloc()
{
	std::ostringstream strConnection_info;
	strConnection_info << "Driver=" << "{MySQL ODBC 9.0 Unicode Driver}" << ";";
	strConnection_info << "server=" << m_Host << ";";
	strConnection_info << "port=" << m_Port << ";";
	strConnection_info << "Database=" << m_DBName << "; ";
	strConnection_info << "User=" << m_UserID << ";";
	strConnection_info << "Password=" << m_Password << ";";
	strConnection_info << "Option=" << "3" << ";";

	NSAdoConnection* connection = new NSAdoConnection();
	if (!connection->Connect(strConnection_info.str().c_str()))
	{
		LOGE << std::format("Connection Fail : {}", strConnection_info.str());
		delete connection;
		return nullptr;
	}

	m_vtAlloc.push_back(connection);

	//ADODB COMMAND 셋팅
	std::unique_ptr<NSAdoRecordset> spList = connection->ExecuteQuery(std::format("SELECT SPECIFIC_NAME FROM information_schema.routines where ROUTINE_SCHEMA = '{}' AND routine_type = 'PROCEDURE'", m_DBName));
	if (spList)
	{
		char			strSpName[256] = { 0 };
		char			strSpNameQuery[1024] = { 0 };
		int64_t			iColLen = 0;
		int64_t			iNumColLen = 0;
		char		ixType[256] = { 0 };
		char		iColKey[256] = { 0 };

		char			strColName[256] = { 0 };
		const char* spParameter = R"(
			SELECT DISTINCT
			PARAMETER_NAME AS ColName, 
			ORDINAL_POSITION AS ColOrder, 
			CHARACTER_MAXIMUM_LENGTH AS ColLen,
			NUMERIC_PRECISION AS NumColLen, 
			PARAMETER_MODE AS ColKey, 
			DATA_TYPE AS xtype 
			FROM 
			INFORMATION_SCHEMA.PARAMETERS
			WHERE 
			SPECIFIC_NAME = '%s'
			AND SPECIFIC_SCHEMA = '%s'
			ORDER BY 
			ColOrder;
		)";

		while (!spList->IsEOF())
		{
			//파라미터 목록을 가져온다.
			memset(strSpName, 0, sizeof(strSpName));
			memset(strSpNameQuery, 0, sizeof(strSpNameQuery));
			spList->GetItem("SPECIFIC_NAME", strSpName, sizeof(strSpName));
			sprintf_s(strSpNameQuery, sizeof(strSpNameQuery), spParameter, strSpName, m_DBName.c_str());

			std::string szSPName(strSpName);
			if (szSPName.substr(0, 2).compare("gm") == 0)
				continue;

			std::unique_ptr<NSAdoRecordset> spParameterList = connection->ExecuteQuery(strSpNameQuery);
			if (spParameterList)
			{
				//커맨드 생성
				NSAdoCommand* pcCmd = new NSAdoMySQLCommand();
				pcCmd->SetProcedure(connection->GetConnection(), strSpName, 10, true);
				//리턴값은 무조건 넣어주자.
				//pcCmd->SetParameter("@ReturnCode", adInteger, adParamOutput, 4);
				/*
				if (strcmp(strSpName, "spLoginAccount") == 0) 
				{
					LOGI << std::format("Init Database Connection {}", strSpName);
				}*/

				while (!spParameterList->IsEOF())
				{
					memset(strColName, 0, sizeof(strColName));
					spParameterList->GetItem("ColName", strColName, sizeof(strColName));
					spParameterList->GetItem("ColLen", iColLen);
					spParameterList->GetItem("NumColLen", iNumColLen);
					spParameterList->GetItem("ColKey", iColKey);
					spParameterList->GetItem("xtype", ixType);

					int64_t resultColLen = iColLen;
					if (iColLen == 0)
					{
						resultColLen = iNumColLen;
					}

					if (!pcCmd->SetParameter(strColName, GetDataTypeEnum(ixType), GetParameterDirectionEnum(iColKey), resultColLen))
					{
						LOGE << std::format("{} SetParameter Fail : {}", strSpName, strColName);
					}
				}				
				connection->AddCommand(strSpName, pcCmd);

				spParameterList->Close();
			}
		}

		spList->Close();
	}

	LOGI << std::format("Init Database Connection {}", m_DBName);

	_ConnectionPtr conn = connection->GetConnection();
	if (conn != nullptr)
	{
		LOGI << std::format("+ Endpoint : {}:{}", m_Host, m_Port);
		LOGI << std::format("+ Version : {}", static_cast<const char*>(conn->GetVersion()));
		LOGI << std::format("+ Connection State : {}", static_cast<ObjectStateEnum>(conn->GetState()));

		PropertiesPtr properties = conn->GetProperties();
		if (properties != nullptr)
		{
			for (std::string name : {
				"Provider Name", "DBMS Name", "DBMS Version", "Driver Name", "Driver Version", "Driver ODBC Version",
					"Multiple Parameter Sets", "Multiple Results"
			})
			{
				PropertyPtr property = properties->GetItem(name.c_str());
				if (property == nullptr)
					continue;

				LOGI << std::format("+ {} : {}",
					static_cast<const char*>(property->GetName()), static_cast<const char*>(_bstr_t { property->GetValue() }));
			}
		}
	}

	return connection;
}

DataTypeEnum NSMySQLConnectionPool::GetDataTypeEnum(std::string_view type)
{
	DataTypeEnum enDataType = DataTypeEnum::adEmpty;

	auto it = m_DataTypeMap.find(type.data());
	if (it != m_DataTypeMap.end())
	{
		return it->second;
	}
	else
	{
		LOGE << std::format("CHECK DATABASE TYPE :{}", type);
	}

	return enDataType;
}

ParameterDirectionEnum NSMySQLConnectionPool::GetParameterDirectionEnum(std::string_view type)
{
	ParameterDirectionEnum enDir = ParameterDirectionEnum::adParamUnknown;

	if (type.compare("IN") == 0)
	{
		enDir = adParamInput;
	}
	else if (type.compare("OUT") == 0)
	{
		enDir = adParamOutput;
	}
	else if (type.compare("INOUT") == 0)
	{
		enDir = adParamInputOutput;
	}
	else
	{
		LOGE << std::format("CHECK DATABASE TYPE :{}", type);
	}

	return enDir;
}
